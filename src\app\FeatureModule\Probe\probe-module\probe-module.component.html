<!------------------------------------------------------------->
<!----------- Probe Listing Page Start ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="isProbeListingPageDisplay">
    <app-ots-probes (showProbeDetail)="showProbeDetail($event)"
        [probeListFilterRequestBody]="probeListFilterRequestBody"
        [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"
        (probeListFilterRequestBodyChange)="updateProbeListFilterRequestBody($event)" [isFilterHidden]="isFilterHidden"
        (isFilterHiddenChange)="updateIsFilterHidden($event)"
        (listPageRefreshForbackToDetailPageFlag)="setListPageRefreshForbackToDetailPageFlag($event)"></app-ots-probes>
</ng-template>
<!------------------------------------------------------------>
<!--------- Probe Listing Page  End-------------------------->
<!------------------------------------------------------------>

<!------------------------------------------------------------->
<!----------- Probe Detail Page Start ------------------------->
<!------------------------------------------------------------->
<ng-template [ngIf]="isProbeDetailPageDisplay">
    <app-ots-probes-detail [probeId]="probeIdInput" (showOtsProbe)="showProbe()"></app-ots-probes-detail>
</ng-template>
<!------------------------------------------------------------>
<!--------- Probe Detail Page  End-------------------------->
<!------------------------------------------------------------>