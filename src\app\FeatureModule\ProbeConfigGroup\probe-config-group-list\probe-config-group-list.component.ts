import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { Component, ViewEncapsulation } from '@angular/core';
import { isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { DateTimeDisplayFormat, DeleteProbeConfigGroupConfirmationMessage, ITEMS_PER_PAGE, ListProbeFearureGroupResource } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ProbeConfigGroupFilterAction } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupFilterAction.model';
import { ProbeConfigGroupListResponse } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupListResponse.model';
import { ProbeConfigGroupRequestBody } from 'src/app/model/ProbeConfigGroup/ProbeConfigGroupRequestBody.model';
import { ProbeConfigGroupPageResponse } from 'src/app/model/ProbeConfigGroup/probeConfigGroupPageResponse.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { ProbeConfigGroupApiCallService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group-api-call.service';
import { ProbeConfigGroupService } from 'src/app/shared/Service/ProbeConfigGroup/probe-config-group.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { ProbeTypeEnum } from 'src/app/shared/enum/ProbeType.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';

@Component({
  selector: 'app-probe-config-group-list',
  templateUrl: './probe-config-group-list.component.html',
  styleUrl: './probe-config-group-list.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class ProbeConfigGroupListComponent {
  loading: boolean = false;

  //subscription
  subscriptionForLoading: Subscription;
  subscriptionForProbeConfigGroupListFilterRequestParameter: Subscription;

  //Probe config Group body store
  probeConfigGroupRequestBody: ProbeConfigGroupRequestBody = null;

  //Filter
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  isFilterComponentInitWithApicall: boolean = true;
  listPageRefreshForbackToDetailPage: boolean = false;

  //Page size dropdown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 0;
  totalItems: number = 0;
  probeConfigGroupId: number

  //Total count display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  probeTypesList: Array<EnumMapping> = [];
  selectedProbeConfigGroupIdList: Array<number> = [];
  localProbeConfigGroupIdList: Array<number> = [];

  probeConfigGroupListResponse: Array<ProbeConfigGroupListResponse> = [];

  showProbeConfigGroupForListingPage: boolean = true;
  showProbeConfigGroupForAddUpdatePage: boolean = false;
  showProbeConfigGroupForDetailPage: boolean = false;

  dateTimeDisplayFormat: string = DateTimeDisplayFormat;

  //Permission
  addProbeConfigGroupPermission: boolean;
  deleteProbeConfigGroupPermission: boolean;


  //unique CheckBox Name
  chkPreFix = 'probeConfigGroup';
  selectAllCheckboxId = 'selectAllProbeConfigGroup';
  checkboxListName = 'probeConfigGroupItem[]';

  constructor(
    private authService: AuthJwtService,
    private commonsService: CommonsService,
    private commonCheckboxService: CommonCheckboxService,
    private probeConfigGroupService: ProbeConfigGroupService,
    private toste: ToastrService,
    private probeConfigGroupApiCallService: ProbeConfigGroupApiCallService,
    private exceptionService: ExceptionHandlingService,
    private dialogservice: ConfirmDialogService,
    private keyValueMappingService: KeyValueMappingServiceService,
    private permissionService: PermissionService,
  ) { }

  public ngOnInit(): void {
    if (!this.authService.isAuthenticate()) {
      this.authService.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.probeTypesList = this.keyValueMappingService.enumOptionToList(ProbeTypeEnum);
      this.drpselectsize = ITEMS_PER_PAGE;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.isFilterHidden = false;
      this.isFilterComponentInitWithApicall = true;
      this.listPageRefreshForbackToDetailPage = false;
      this.addProbeConfigGroupPermission = this.permissionService.getProbeConfigGroupPermission(PermissionAction.ADD_CONFIG_GROUP_ACTION);
      this.deleteProbeConfigGroupPermission = this.permissionService.getProbeConfigGroupPermission(PermissionAction.DELETE_CONFIG_GROUP_ACTION)
      this.subjectInit();
    }
  }

  public refreshFilter(): void {
    this.resetPage();
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;
    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Item par page Value Changes like (10,50,100)
   * <AUTHOR>
   * @param datasize
   */
  public changeDataSize(datasize): void {
    this.setLoadingStatus(true);
    this.selectedProbeConfigGroupIdList = [];
    this.itemsPerPage = datasize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  private hideShowProbeConfigGroupPages(listingPage: boolean, deatilePage: boolean, addUpdatePage: boolean): void {
    this.showProbeConfigGroupForListingPage = listingPage;
    this.showProbeConfigGroupForAddUpdatePage = addUpdatePage;
    this.showProbeConfigGroupForDetailPage = deatilePage;
  }

  /**
   * Show Add Probe config group page
   * <AUTHOR>
   */
  public showAddProbeConfigGroupPage() {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = false;
    this.hideShowProbeConfigGroupPages(false, false, true);
  }

  /**
   * Show Probe config group listing page 
   * <AUTHOR>
   */
  public showProbeConfigGroupListPage() {
    this.isFilterComponentInitWithApicall = false;
    this.listPageRefreshForbackToDetailPage = true;
    this.hideShowProbeConfigGroupPages(true, false, false)
  }

  /**
   * Show probe config group detail page
   * <AUTHOR>
   * @param id 
   */
  public setProbrFeatureGroupId(id: number): void {
    this.probeConfigGroupId = id;
    this.hideShowProbeConfigGroupPages(false, true, false)
  }

  /**
   * Select All CheckBox
   * <AUTHOR>
   * @param isChecked
   */
  public selectAllItem(isChecked: boolean): void {
    this.selectedProbeConfigGroupIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localProbeConfigGroupIdList, this.selectedProbeConfigGroupIdList, this.checkboxListName);
  }

  /**
   * single Checkbox Select
   * <AUTHOR>
   * @param roleObj
   * @param isChecked
   */
  public selectCheckbox(probeConfigGroupObj: any, isChecked: boolean): void {
    if (isChecked) {
      this.selectedProbeConfigGroupIdList.push(probeConfigGroupObj.id);
    } else {
      let index = this.selectedProbeConfigGroupIdList.findIndex((obj) => obj == probeConfigGroupObj.id);
      this.selectedProbeConfigGroupIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
    * Change The Page
    * callProbeConfigGroupListRefreshSubject ->Call the filter component
    * filter not clear and send with filter requrest and load data 
    * <AUTHOR>
    * @param page 
    */
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.commonCheckboxService.clearSelectAllCheckbox(this.selectAllCheckboxId);
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }


  /**
   * select All checkbox select or deSelect
   * <AUTHOR>
   */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localProbeConfigGroupIdList,
      this.selectedProbeConfigGroupIdList, this.selectAllCheckboxId);
  }

  /**
   * Loading Status
   * <AUTHOR>
   */
  private setLoadingStatus(status: boolean): void {
    this.loading = status;
  }

  /**
   * Reset Page
   * <AUTHOR>
   */
  private resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }

  private subjectInit(): void {

    /**
     * This Subject call from Filter component
     * Load all the Data
     * <AUTHOR>
     */
    this.subscriptionForProbeConfigGroupListFilterRequestParameter =
      this.probeConfigGroupService
        .getProbeConfigGroupListFilterRequestParameterSubject()
        ?.subscribe(
          (probeFearureGroupRequestParameter: ProbeConfigGroupFilterAction) => {
            if (probeFearureGroupRequestParameter.listingPageReloadSubjectParameter.isReloadData) {
              if (probeFearureGroupRequestParameter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
                this.selectedProbeConfigGroupIdList = [];
                this.resetPage();
              }
              this.loadAll(probeFearureGroupRequestParameter.probeConfigGroupRequestBody);
            }
          }
        );
  }

  /**
   * Get Probe Feature Group List
   *
   * <AUTHOR>
   *
   * @param salesOrderRequestBody
   */
  public loadAll(probeConfigGroupRequestBody: ProbeConfigGroupRequestBody): void {
    this.probeConfigGroupRequestBody = probeConfigGroupRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage,
    };
    this.setLoadingStatus(true);
    this.probeConfigGroupApiCallService.getProbeConfigGroupList(probeConfigGroupRequestBody, pageObj)?.subscribe({
      next: (probeConfigGroupPageResponse: HttpResponse<ProbeConfigGroupPageResponse>) => {
        if (probeConfigGroupPageResponse.status == 200) {
          this.paginateDataset(probeConfigGroupPageResponse.body);
        } else {
          this.probeConfigGroupListResponse = [];
          this.totalRecordDisplay = 0;
          this.totalRecord = 0;
        }
        this.setLoadingStatus(false);
      },
      error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      },
    });
  }

  /**
   * Call Filter component subject and reload page
   * <AUTHOR> @param isDefaultPageNumber
   * @param isClearFilter
   */
  public filterPageSubjectCallForReloadPage(
    isDefaultPageNumber: boolean,
    isClearFilter: boolean
  ): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.probeConfigGroupService.callRefreshPageSubject(listingPageReloadSubjectParameter, ListProbeFearureGroupResource, this.isFilterHidden, this.probeConfigGroupRequestBody);
  }

  /**
   * Delete Sales Order
   *
   * <AUTHOR>
   */
  public deleteProbeConfigGroupOperation(): void {
    if (this.selectedProbeConfigGroupIdList.length > 0) {
      this.dialogservice.confirm('Delete', DeleteProbeConfigGroupConfirmationMessage)
        .then((confirmed) => {
          if (confirmed) {
            this.setLoadingStatus(true);
            this.probeConfigGroupApiCallService.deleteProbeConfigGroup(this.selectedProbeConfigGroupIdList)?.subscribe({
              next: (res: HttpResponse<SuccessMessageResponse>) => {
                this.toste.success(res.body.message);
                this.filterPageSubjectCallForReloadPage(true, true);
                this.selectedProbeConfigGroupIdList = [];
              },
              error: (error: HttpErrorResponse) => {
                this.setLoadingStatus(false);
                this.exceptionService.customErrorMessage(error);
              }
            })
          }
        });
    } else {
      this.toste.info('Please Select a Probe Feature Group');
    }
  }

  /**
   * Probe Config Group Response set
   * <AUTHOR>
   * @param rolePageResponse
   */
  private paginateDataset(
    probeConfigGroupPageResponse: ProbeConfigGroupPageResponse
  ): void {
    this.totalItems = probeConfigGroupPageResponse.totalElements;
    this.probeConfigGroupListResponse = probeConfigGroupPageResponse.content;
    this.page = probeConfigGroupPageResponse.number + 1;
    this.totalRecord = probeConfigGroupPageResponse.totalElements;
    this.totalRecordDisplay = probeConfigGroupPageResponse.numberOfElements;
    this.setLocalProbeConfigGroupId(this.probeConfigGroupListResponse);
    this.setLoadingStatus(false);
  }

  /**
   * Local Probe Config Group Id list create for Select all Checkbox
   * <AUTHOR>
   * @param probeConfigGroupIdList
   */
  public setLocalProbeConfigGroupId(probeConfigGroupIdList: ProbeConfigGroupListResponse[]): void {
    this.localProbeConfigGroupIdList = [];
    for (let probeConfigGroupObj of probeConfigGroupIdList) {
      this.localProbeConfigGroupIdList.push(probeConfigGroupObj.id);
    }
    this.defaultSelectAll();
  }

  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForProbeConfigGroupListFilterRequestParameter)) { this.subscriptionForProbeConfigGroupListFilterRequestParameter.unsubscribe() }
    this.probeConfigGroupService.setFeaturesList([]);
    this.probeConfigGroupService.setPresetsList([]);
  }

}
