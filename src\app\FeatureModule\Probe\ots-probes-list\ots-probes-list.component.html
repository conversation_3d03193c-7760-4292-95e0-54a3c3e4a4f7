<!-- loading start -->
<div class="ringLoading" *ngIf="loading">
  <!-- loading gif start -->
  <div class="ringLoadingDiv">
    <img src="assets/DoubleRing.gif" alt="loading" *ngIf="loading">
  </div>
  <!-- loading gif end -->
</div>
<!-- loading end -->

<body>
  <!-- row start -->
  <div class="row" *ngIf="displayOts">
    <!--Filter start-->
    <div class="col-md-3 pr-0" *ngIf="(!isFilterHidden)">
      <!-- filter header start -->
      <label class="col-md-12 h5-tag">Filter</label>
      <!-- filter header end -->
      <div class="card mt-3">
        <!-- card body start -->
        <div class="card-body">
          <app-ots-probes-filter [isFilterComponentInitWithApicall]="isFilterComponentInitWithApicall"
            [probeListFilterRequestBody]="probeListFilterRequestBody"
            [listPageRefreshForbackToDetailPage]="listPageRefreshForbackToDetailPage"
            (listToModuleConnect)="setListToModuleConnect($event)"></app-ots-probes-filter>
        </div>
        <!-- card body end -->
      </div>
    </div>
    <!--Filter End-->
    <!--table Block Start-->
    <div [className]="(isFilterHidden)?'col-md-12 pr-0':'col-md-9 pr-0'">
      <div class="container-fluid">
        <!--view Probe size Start-->
        <div class="row" class="headerAlignment">
          <!--------------------------------------->
          <!--Left Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!----------------------------------------------->
            <!------------Show/hide filter-------------------->
            <!----------------------------------------------->
            <div class="dropdown" id="otsProbesHideShowFilter">
              <button class="btn btn-sm btn-orange mr-3 ml-0 filter-symbol" (click)="toggleFilter()"
                [id]="(isFilterHidden)?'Show Filter':'Hide Filter'">
                <i class="fas fa-filter" aria-hidden="true" [id]="(isFilterHidden)?'Show Filter':'Hide Filter'"></i>
                &nbsp;&nbsp;{{ hideShowFilterButtonText }}
              </button>
            </div>
            <!----------------------------------------------->
            <!------------Pagnatation drp-------------------->
            <!----------------------------------------------->
            <div>
              <label class="mb-0">Show entry</label>
              <select id="otsProbesShowEntry" [(ngModel)]="drpselectsize" class="form-control form-control-sm"
                (change)="changeDataSize($event)">
                <ng-template ngFor let-dataSize [ngForOf]="dataSizes">
                  <option [value]="dataSize">{{ dataSize }}</option>
                </ng-template>
              </select>
            </div>
          </div>
          <!--------------------------------------->
          <!--Right Side-->
          <!--------------------------------------->
          <div class="childFlex">
            <!------------------------------------------------->
            <!--------------Probe Operations------------------->
            <!------------------------------------------------->
            <div *ngIf="probeOperations.length > 1" class="mr-3">
              <select id="probeOperation" class="form-control form-control-sm" (change)="changeProbeOperation($event)">
                <ng-template ngFor let-probeOperation [ngForOf]="probeOperations">
                  <option [value]="probeOperation">{{ probeOperation }}</option>
                </ng-template>
              </select>
            </div>
            <!----------------------------------------->
            <!--------------NEW Probe------------------>
            <!----------------------------------------->
            <div *ngIf="isAddProbeBtnDisplay">
              <button *ngIf="addProbPermission" class="btn btn-sm btn-orange mr-2" (click)="otsProbeAddUpdate()"
                id="addProbeBtn">
                <em class="fa fa-plus"></em> &nbsp;&nbsp;New Probe
              </button>
            </div>
            <div>
              <button class="btn btn-sm btn-outline-secondary mr-2" (click)="downloadPdf()"><i class="fa fa-download"
                  aria-hidden="true" *ngIf="downloadSalesOrderLetterPermission"></i>&nbsp;&nbsp;Download SO
                Letter</button>
            </div>
            <!------------------------------------------------>
            <!----------------refresh------------------------->
            <!------------------------------------------------>
            <div>
              <button class="btn btn-sm btn-orange" (click)="clickOnRefreshButton()" id="refresh_ProbeList"><em
                  class="fa fa-refresh"></em></button>
            </div>
          </div>
        </div>

        <!-- selected probes start -->
        <div>Total {{totalProbes}} Probes
          <p *ngIf="probeIdList != null && probeIdList.length > 0">
            <strong>{{probeIdList.length}} Probe(s) selected</strong>
          </p>
        </div>
        <!-- selected probes end -->

        <div class="probe-table">
          <!-- probe table start -->
          <table class="table table-sm table-bordered" style="overflow-x: scroll;" aria-hidden="true">
            <!-- probe table header start -->
            <thead>
              <tr class="thead-light">
                <th *ngIf="probeOperations.length > 0" class="checkox-table width-unset">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" name="chkseleclall" id="selectAllProbe"
                      (change)="selectAllProbe($event)">
                    <label class="custom-control-label" for="selectAllProbe"></label>
                  </div>
                </th>
                <th><span>{{serialNo}}</span></th>
                <th class="min_column_width"><span>{{probeType}}</span></th>
                <th><span>{{presets}}</span></th>
                <th><span>{{features}}</span></th>
                <th><span>{{salesOrderNumber}}</span></th>
                <th><span>{{customerName}}</span></th>
                <th><span>{{country}}</span></th>
                <th class="min_column_width"><span>{{appVersion}}</span></th>
                <th class="min_column_width"><span>{{osType}}</span></th>
                <th class="min_column_width"><span>{{osVersion}}</span></th>
                <th><span>{{lastConnectedDateAndTime}}</span></th>
                <th class="min_column_width"><span>{{status}}</span></th>
                <th class="min_column_width"><span>{{editable}}</span></th>
                <th class="min_column_width"><span>{{locked}}</span></th>
              </tr>
            </thead>
            <!-- probe table header end -->
            <!-- probe table body start -->
            <tbody>
              <tr *ngFor="let probe of probes; let probeIndex = index">
                <td *ngIf="probeOperations.length > 0" class="width-unset">
                  <div class="custom-control custom-checkbox">
                    <input type="checkbox" class="custom-control-input" [id]="'probe_'+ probe.id" name="probe[]"
                      [checked]="defaultSelectProbe(probe.id)" (change)="onChangeProbe(probe, $event)">
                    <label class="custom-control-label" [for]="'probe_' + probe.id"></label>
                  </div>
                </td>
                <td class=" min_column_width spanunderline" id="probeDetails" (click)="openProbeDetail(probe)">
                  {{probe.serialNumber}}</td>
                <td>{{probe.type}}</td>
                <td><span>{{probe.presets}}</span></td>
                <td><span>{{probe.features}}</span></td>
                <td><span>{{probe.salesOrderNumber}}</span></td>
                <td><span>{{probe.customerName}}</span></td>
                <td><span>{{probe.country}}</span></td>
                <td class="min_column_width">{{probe.appVersion}}</td>
                <td class="min_column_width"><span>{{probe.osType}}</span></td>
                <td class="min_column_width"><span>{{probe.osVersion}}</span></td>
                <td>{{probe.lastConnectedTime | date:dateTimeDisplayFormat}}</td>
                <td class="min_column_width">{{probe?.productStatus | enumMappingDisplayNamePipe:productStatusList}}
                </td>
                <td style="text-align: center;" class="min_column_width">
                  <ng-container *ngIf="probe.editable">
                    <div title="Allowed"><span class="fa fa-pencil-alt" style="font-size: 18px; color: #f67409;"></span>
                    </div>
                  </ng-container>
                  <ng-container *ngIf="!probe.editable">
                    <div title="Not Allowed"><img alt="edit Disable" src="assets/images/editd.png"></div>
                  </ng-container>
                </td>
                <td class="min_column_width">
                  <div class="lockedDiv" *ngIf="probe?.locked != null" [attr.title]="probe.locked ? 'Lock' : 'Unlock'">
                    <span [ngClass]="[ probe?.locked ? 'lock' : 'lock unlocked']"></span>
                  </div>
                </td>
              </tr>
            </tbody>
            <!-- probe table body end -->
          </table>
          <!-- probe table end -->
        </div>

        <!--pagination Start-->
        <div>
          <div>Showing {{totalProbeDisplay}} out of {{totalProbes}} Probes</div>
          <div class="float-right">
            <!-- ngb pagination start -->
            <ngb-pagination [collectionSize]="totalItems" [(page)]="page" [pageSize]="itemsPerPage" [maxSize]="5"
              id="probe-pagination" [rotate]="true" [boundaryLinks]="true" (pageChange)="loadPage(page)">
            </ngb-pagination>
            <!-- ngb pagination end -->
          </div>
        </div>
        <!--pagination End-->
      </div>
    </div>
    <!--table Block End-->
  </div>
  <!-- row end -->
</body>

<div *ngIf="displayOTSDetail">
  <app-ots-probes-detail [probeId]="probeId" [resource]="probeListResource"
    (showOtsProbe)="showOtsProbe()"></app-ots-probes-detail>
</div>

<div *ngIf="displayOtsProbeAddUpdate">
  <app-create-update-multiple-probe (showOtsProbe)="showOtsProbe()"></app-create-update-multiple-probe>
</div>