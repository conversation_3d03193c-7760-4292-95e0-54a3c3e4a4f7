
import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NgMultiSelectDropDownModule } from 'ng-multiselect-dropdown';
import { ToastrService } from 'ngx-toastr';
import { LocalStorageService, SessionStorageService } from 'ngx-webstorage';
import { of, throwError } from 'rxjs';
import { INTERNAL_SERVER_ERROR } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { BasicModelConfig } from 'src/app/model/common/BasicModelConfig.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { SoftwareBuildListResponse } from 'src/app/model/SoftwaarBuilds/SoftwareBuildListResponse.model';
import { Jsonlist } from 'src/app/model/video/jsonlist.model';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { deviceTypesEnum } from 'src/app/shared/enum/deviceTypesEnum.enum';
import { SoftwareBuildStatusEnum } from 'src/app/shared/enum/SoftwareBuildStatusEnum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { SSOLoginService } from 'src/app/shared/Service/SSO/ssologin.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { commonsProviders } from 'src/app/Tesing-Helper/test-utils';
import { SoftwareBuildApiCallService } from '../software-build-services/software-api-call/software-build-api-call.service';
import { EditSoftwareBuildComponent } from './edit-software-build.component';

describe('EditSoftwareBuildComponent', () => {
  let component: EditSoftwareBuildComponent;
  let fixture: ComponentFixture<EditSoftwareBuildComponent>;
  let toastrServiceMock: jasmine.SpyObj<ToastrService>;
  let exceptionHandlingService: ExceptionHandlingService;
  let softwareBuildApiCallspy: jasmine.SpyObj<SoftwareBuildApiCallService>;
  let downloadService: jasmine.SpyObj<DownloadService>;
  let activeModalSpy: jasmine.SpyObj<NgbActiveModal>;


  beforeEach(async () => {
    toastrServiceMock = jasmine.createSpyObj('ToastrService', ['success', 'error', 'warning', 'info']);
    softwareBuildApiCallspy = jasmine.createSpyObj('SoftwareBuildApiCallService', ['updateInventory']);
    downloadService = jasmine.createSpyObj('DownloadService', ['downloadMyFile', 'getisLoadingSubject', 'setLoading']);
    activeModalSpy = jasmine.createSpyObj('NgbActiveModal', ['close']);

    await TestBed.configureTestingModule({
      declarations: [EditSoftwareBuildComponent],
      imports: [NgMultiSelectDropDownModule.forRoot(), ReactiveFormsModule, FormsModule],
      providers: [
        { provide: NgbActiveModal, useValue: activeModalSpy },
        CommonsService,
        LocalStorageService,
        ConfirmDialogService,
        ExceptionHandlingService,
        AuthJwtService,
        SessionStorageService,
        SSOLoginService,
        { provide: SoftwareBuildApiCallService, useValue: softwareBuildApiCallspy },
        { provide: DownloadService, useValue: downloadService },
        commonsProviders(toastrServiceMock)
      ]
    })
      .compileComponents();

    fixture = TestBed.createComponent(EditSoftwareBuildComponent);
    exceptionHandlingService = TestBed.inject(ExceptionHandlingService);
    component = fixture.componentInstance;

    // Setup default mock data
    component.countryList = [
      new CountryListResponse(1, 'USA', ['English']),
      new CountryListResponse(2, 'Canada', ['English', 'French'])
    ];
    component.jsonVersionList = [
      new Jsonlist(1, 'v1.0'),
      new Jsonlist(2, 'v2.0')
    ];
    component.inventory = {
      id: 1,
      version: 'test-version',
      partNumber: 'test-part',
      isActive: true,
      deviceTypes: [deviceTypesEnum.CLIENT_DEVICE],
      countries: ['USA'],
      jsonMaster: new Jsonlist(1, 'v1.0')
    } as SoftwareBuildListResponse;

    fixture.detectChanges(); // Triggers ngOnInit
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // ==================== COMPONENT INITIALIZATION TESTS ====================

  describe('Component Initialization', () => {
    it('should initialize form with default values', () => {
      expect(component.form).toBeDefined();
      expect(component.form.get('partNumber')).toBeDefined();
      expect(component.form.get('deviceTypes')).toBeDefined();
      expect(component.form.get('inventoryStatus')).toBeDefined();
      expect(component.form.get('country')).toBeDefined();
      expect(component.form.get('jsonVersion')).toBeDefined();
    });

    it('should initialize input properties', () => {
      expect(component.countryList).toBeDefined();
      expect(component.jsonVersionList).toBeDefined();
      expect(component.inventory).toBeDefined();
      expect(component.basicModelConfig).toBeUndefined();
    });

    it('should call setFormValue on ngOnInit', () => {
      spyOn(component, 'setFormValue' as any);

      component.ngOnInit();

      expect(component['setFormValue']).toHaveBeenCalled();
    });
  });

  // ==================== FORM VALIDATION TESTS ====================

  describe('Form Validation', () => {
    it('should set form values from inventory data', () => {
      component['setFormValue']();

      expect(component.form.get('partNumber').value).toBe('test-part');
      expect(component.form.get('deviceTypes').value).toEqual(["Client Device"]);
      expect(component.form.get('inventoryStatus').value).toEqual([SoftwareBuildStatusEnum.ACTIVE]);
    });

    it('should handle null inventory gracefully', () => {
      component.inventory = null;

      expect(() => component['setFormValue']()).not.toThrow();
    });

    it('should handle inventory without countries', () => {
      component.inventory.countries = null;

      expect(() => component['setFormValue']()).not.toThrow();
    });

    it('should handle inventory without jsonMaster', () => {
      component.inventory.jsonMaster = null;

      expect(() => component['setFormValue']()).not.toThrow();
    });
  });

  // ==================== ACCEPT METHOD TESTS ====================

  describe('Accept Method', () => {
    beforeEach(() => {
      // Setup form with valid values
      component.form.get('deviceTypes').setValue([deviceTypesEnum.CLIENT_DEVICE]);
      component.form.get('inventoryStatus').setValue([SoftwareBuildStatusEnum.ACTIVE]);
      component.form.get('country').setValue([{ id: 1, country: 'USA' }]);
      component.form.get('jsonVersion').setValue([{ id: 1, name: 'v1.0' }]);
      component.form.get('partNumber').setValue('test-part-updated');
    });

    it('should update inventory successfully', () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { message: 'Updated successfully' }
      });

      softwareBuildApiCallspy.updateInventory.and.returnValue(of(mockResponse));

      component.accept();

      expect(softwareBuildApiCallspy.updateInventory).toHaveBeenCalled();
      expect(activeModalSpy.close).toHaveBeenCalledWith(true);
      expect(toastrServiceMock.success).toHaveBeenCalledWith('Updated successfully');
    });

    it('should handle API error', () => {
      const mockError = new HttpErrorResponse({ status: 500, statusText: 'Server Error' });

      softwareBuildApiCallspy.updateInventory.and.returnValue(throwError(() => mockError));
      spyOn(exceptionHandlingService, 'customErrorMessage').and.callThrough();

      component.accept();

      expect(exceptionHandlingService.customErrorMessage).toHaveBeenCalledWith(mockError);
      expect(toastrServiceMock.error).toHaveBeenCalledWith(INTERNAL_SERVER_ERROR);
    });

    it('should handle non-200 response status', () => {
      const mockResponse = new HttpResponse({
        status: 400,
        body: { message: 'Bad request' }
      });

      softwareBuildApiCallspy.updateInventory.and.returnValue(of(mockResponse));

      component.accept();

      expect(activeModalSpy.close).toHaveBeenCalledWith(true);
    });

    it('should handle response without body', () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: null
      });

      softwareBuildApiCallspy.updateInventory.and.returnValue(of(mockResponse));

      component.accept();

      expect(toastrServiceMock.success).not.toHaveBeenCalled();
    });

    it('should create correct edit request object', () => {
      const mockResponse = new HttpResponse({
        status: 200,
        body: { message: 'Updated successfully' }
      });

      softwareBuildApiCallspy.updateInventory.and.returnValue(of(mockResponse));

      component.accept();

      const expectedRequest = jasmine.objectContaining({
        deviceTypes: jasmine.any(Array),
        isActive: jasmine.any(Boolean),
        jsonMaster: jasmine.any(Object),
        countryIds: jasmine.any(Array),
        partNumber: 'test-part-updated'
      });

      expect(softwareBuildApiCallspy.updateInventory).toHaveBeenCalledWith(
        component.inventory.id,
        expectedRequest
      );
    });
  });

  // ==================== DECLINE METHOD TESTS ====================

  describe('Decline Method', () => {
    it('should close modal with false when decline is called', () => {
      component.decline();

      expect(activeModalSpy.close).toHaveBeenCalledWith(false);
    });
  });

  // ==================== INPUT PROPERTIES TESTS ====================

  describe('Input Properties', () => {
    it('should accept basicModelConfig input', () => {
      const mockConfig = new BasicModelConfig('Test Title', 'Test Body', 'OK', 'Cancel');
      component.basicModelConfig = mockConfig;

      expect(component.basicModelConfig).toBe(mockConfig);
    });

    it('should accept countryList input', () => {
      const mockCountryList = [
        new CountryListResponse(1, 'USA', ['English']),
        new CountryListResponse(2, 'Canada', ['English', 'French'])
      ];
      component.countryList = mockCountryList;

      expect(component.countryList).toBe(mockCountryList);
    });

    it('should accept jsonVersionList input', () => {
      const mockJsonList = [
        new Jsonlist(1, 'v1.0'),
        new Jsonlist(2, 'v2.0')
      ];
      component.jsonVersionList = mockJsonList;

      expect(component.jsonVersionList).toBe(mockJsonList);
    });

    it('should accept inventory input', () => {
      const mockInventory = {
        id: 2,
        version: 'test-version-2',
        partNumber: 'test-part-2',
        isActive: false,
        deviceTypes: [deviceTypesEnum.DEMO_DEVICE],
        countries: ['Canada'],
        jsonMaster: new Jsonlist(2, 'v2.0')
      } as SoftwareBuildListResponse;

      component.inventory = mockInventory;

      expect(component.inventory).toBe(mockInventory);
    });
  });

  // ==================== FORM INTERACTION TESTS ====================

  describe('Form Interactions', () => {
    it('should handle form value changes', () => {
      component.form.get('partNumber').setValue('new-part-number');
      component.form.get('deviceTypes').setValue([deviceTypesEnum.DEMO_DEVICE]);
      component.form.get('inventoryStatus').setValue([SoftwareBuildStatusEnum.INACTIVE]);

      expect(component.form.get('partNumber').value).toBe('new-part-number');
      expect(component.form.get('deviceTypes').value).toEqual([deviceTypesEnum.DEMO_DEVICE]);
      expect(component.form.get('inventoryStatus').value).toEqual([SoftwareBuildStatusEnum.INACTIVE]);
    });

    it('should handle form validation', () => {
      component.form.get('partNumber').setValue('valid-part-number');

      expect(component.form.get('partNumber').value).toBe('valid-part-number');
      expect(component.form.valid).toBeFalsy();
    });
  });
});
