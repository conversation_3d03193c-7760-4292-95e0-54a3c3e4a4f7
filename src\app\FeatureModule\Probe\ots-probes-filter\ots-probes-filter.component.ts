import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import {
  FILTER, FILTER_CLEAR_BUTTON, FILTER_SEARCH_BUTTON, MAXIMUM_TEXTBOX_LENGTH, MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE,
  ProbListResource, PROBE_COUNTRY, PROBE_CUSTOMER_NAME, PROBE_DEVICE_HISTORICAL_DATA, PROBE_DEVICE_MODEL,
  PROBE_FEATURE_VALIDITY_PERIOD, PROBE_FEATURES, PROBE_MANUFACTURER, PROBE_OS_TYPE, PROBE_PRESETS,
  PROBE_SALES_ORDER_NUMBER, PROBE_SERIAL_NO, PROBE_STATUS, PROBE_TYPE_FILTER, SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE,
  SMALL_TEXTBOX_MAX_LENGTH, SPECIAL_CHARACTER_ERROR_MESSAGE, SPECIAL_CHARACTER_PATTERN,
  LOCKED, EDITABLE
} from 'src/app/app.constants';
import { BooleanKeyValueMapping } from 'src/app/model/common/BooleanKeyValueMapping.model';
import { EnumMapping } from 'src/app/model/common/EnumMapping.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { MultiSelectDropdownSettings } from 'src/app/model/MultiSelectDropdownSettings.model';
import { PresetDetailBaseResponse } from 'src/app/model/Presets/PresetDetailBaseResponse.model';
import { FeaturesFilter } from 'src/app/model/probe/FeaturesFilter.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { ProductStatusEnum } from 'src/app/shared/enum/Common/ProductStatus.enum';
import { OSTypeEnum } from 'src/app/shared/enum/Probe/OSTypeEnum.enum';
import { DeviceHistoricalData } from 'src/app/shared/enum/Probe/DeviceHistoricalData.enum';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { MultiSelectDropDownSettingService } from 'src/app/shared/util/multi-select-drop-down-setting.service';
import { ProbeOperationService } from '../ProbeService/Probe-Operation/probe-operation.service';
import { ProbeFilterAction } from 'src/app/model/probe/ProbeFilterAction.model';

@Component({
  selector: 'app-ots-probes-filter',
  templateUrl: './ots-probes-filter.component.html',
  styleUrl: './ots-probes-filter.component.css'
})
export class OtsProbesFilterComponent implements OnInit, OnDestroy {

  @Input("isFilterComponentInitWithApicall") isFilterComponentInitWithApicall: boolean = true;
  @Input("listPageRefreshForbackToDetailPage") listPageRefreshForbackToDetailPage: boolean = false;
  @Input("probeListFilterRequestBody") probeListFilterRequestBody: ProbeListFilterRequestBody;
  @Output("listToModuleConnect") listToModuleConnect = new EventEmitter<boolean>();

  // multiselect
  ShowFilter = false;
  limitSelection = false;
  cities: Array<any> = [];
  selectedItems: Array<any> = [];
  dropdownSettingsForProbeType: MultiSelectDropdownSettings = null;
  dropdownSettingsForSalesOrder: MultiSelectDropdownSettings = null;
  dropdownSettingsJobType: MultiSelectDropdownSettings = null;
  dropdownSettingsFeature: MultiSelectDropdownSettings = null;
  dropdownSettingsFeatureValidityPeriod: MultiSelectDropdownSettings = null;
  dropdownSettingsForOsType: MultiSelectDropdownSettings = null;
  dropdownSettingsForProductStatus: MultiSelectDropdownSettings = null;
  dropdownSettingsForCountry: MultiSelectDropdownSettings = null;
  dropdownSettingsForLockState: MultiSelectDropdownSettings = null;
  dropdownSettingsForEditState: MultiSelectDropdownSettings = null;
  dropdownSettingsPreset: MultiSelectDropdownSettings = null;

  // filter
  salesOrderNumberList: any[];
  probeTypesList: any[];
  featuresList: Array<FeaturesFilter> = [];
  presetList: Array<PresetDetailBaseResponse> = [];
  featureValidityPeriodList: BooleanKeyValueMapping[] = [];
  lockUnlockStateList: Array<BooleanKeyValueMapping> = [];
  editStateList: Array<BooleanKeyValueMapping> = [];
  osTypeList: Array<EnumMapping> = [];
  productStatusList: Array<EnumMapping> = [];
  countryList: CountryListResponse[] = [];
  probeListResource = ProbListResource;

  // Subscriptions
  subscriptionForRefreshList: Subscription;

  // Default listing page reload parameters
  defaultListingPageReloadSubjectParameter: ListingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);

  constructor(
    private fb: FormBuilder,
    private multiSelectDropDownSettingService: MultiSelectDropDownSettingService,
    private keyValueMappingServiceService: KeyValueMappingServiceService,
    private commonsService: CommonsService,
    private probeOperationService: ProbeOperationService,
  ) { }

  // Text box validation messages
  textBoxMaxCharactersAllowedMessage: string = MEXIMUM_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  small_textBoxMaxCharactersAllowedMessage: string = SMALL_TEXTBOX_MAX_CHARACTERS_ALLOWED_MESSAGE;
  specialCharacterErrorMessage: string = SPECIAL_CHARACTER_ERROR_MESSAGE;

  // Filter Constants
  filter: string = FILTER;
  searchBtnText: string = FILTER_SEARCH_BUTTON;
  clearBtnText: string = FILTER_CLEAR_BUTTON;
  serialNo: string = PROBE_SERIAL_NO;
  salesOrderNumber: string = PROBE_SALES_ORDER_NUMBER;
  country: string = PROBE_COUNTRY;
  customerName: string = PROBE_CUSTOMER_NAME;
  status: string = PROBE_STATUS;
  probeType: string = PROBE_TYPE_FILTER;
  features: string = PROBE_FEATURES;
  presets: string = PROBE_PRESETS;
  deviceModel: string = PROBE_DEVICE_MODEL;
  manufacturer: string = PROBE_MANUFACTURER;
  osType: string = PROBE_OS_TYPE;
  featureValidityPeriod: string = PROBE_FEATURE_VALIDITY_PERIOD;
  deviceHistoricalData: string = PROBE_DEVICE_HISTORICAL_DATA;
  locked: string = LOCKED;
  editable: string = EDITABLE;

  // Filter form - will be initialized in ngOnInit
  filterForm: FormGroup;

  ngOnInit(): void {
    this.initializeFilterForm();
    this.initializeDropdownSettings();
    this.initializeDropdownData();
    this.getInitCall();
    this.onInitSubject();
    if (this.isFilterComponentInitWithApicall && !this.listPageRefreshForbackToDetailPage) {
      this.clearFilter();
    }
    this.setFilterValue();
  }

  /**
  * Initialize the filter form with all form controls
  * <AUTHOR>
  */
  private initializeFilterForm(): void {
    this.filterForm = this.fb.group({
      salesOrderNumber: [[], []],
      serialNumber: ['', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]],
      probeTypes: [[], []],
      probeFeatures: [[], []],
      presetType: [[], []],
      customerName: ['', [Validators.maxLength(MAXIMUM_TEXTBOX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]],
      deviceModel: ['', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]],
      manufacturer: ['', [Validators.maxLength(SMALL_TEXTBOX_MAX_LENGTH), Validators.pattern(SPECIAL_CHARACTER_PATTERN)]],
      featureValidityPeriod: [[], []],
      osType: [[], []],
      productStatus: [[], []],
      deviceHistoricalData: ['', []],
      countries: [[], []],
      lockState: [[], []],
      probeEditState: [[], []]
    });
  }

  /**
  * Initialize dropdown settings
  * <AUTHOR>
  */
  private initializeDropdownSettings(): void {
    this.dropdownSettingsForProbeType = this.multiSelectDropDownSettingService.getProbeTypeDropdownSetting();
    this.dropdownSettingsForSalesOrder = this.multiSelectDropDownSettingService.getSalesOrderNumberDrpSetting(false, 'Select All', 'UnSelect All', false);
    this.dropdownSettingsFeature = this.multiSelectDropDownSettingService.getFeatureDrpSetting();
    this.dropdownSettingsJobType = this.multiSelectDropDownSettingService.getJobTypeDropdownSetting();
    this.dropdownSettingsFeatureValidityPeriod = this.multiSelectDropDownSettingService.getFeatureValidityPeriodDrpSetting();
    this.dropdownSettingsForOsType = this.multiSelectDropDownSettingService.getOTSTypeDrpSetting();
    this.dropdownSettingsForProductStatus = this.multiSelectDropDownSettingService.getProductStatusDrpSetting();
    this.dropdownSettingsForCountry = this.multiSelectDropDownSettingService.getCountryDrpSetting(false, false);
    this.dropdownSettingsForLockState = this.multiSelectDropDownSettingService.getLockStateDropdownSetting();
    this.dropdownSettingsForEditState = this.multiSelectDropDownSettingService.getEditStateDropdownSetting();
    this.dropdownSettingsPreset = this.multiSelectDropDownSettingService.getPresetDrpSetting();

    this.lockUnlockStateList = this.keyValueMappingServiceService.lockedUnlockOptionList();
    this.editStateList = this.keyValueMappingServiceService.editEnableDisableOptionList();
  }

  /**
  * Initialize subject subscriptions
  * <AUTHOR>
  */
  public onInitSubject(): void {
    this.subscriptionForRefreshList = this.probeOperationService?.getProbeListRefreshSubject()?.subscribe((listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter) => {
      if (listingPageReloadSubjectParameter.isReloadData) {
        if (listingPageReloadSubjectParameter.isClearFilter) {
          this.clearAllFilter();
          this.probeOperationService.clearAllFiltersAndRefresh(listingPageReloadSubjectParameter);
        } else {
          //page change 1,2,3
          this.probeListPageRefresh(listingPageReloadSubjectParameter)
        }
      }
    }
    );
  }

  /**
  * Get initial data for dropdowns using cached data from operation service
  * Only makes API calls if cache is empty, otherwise uses cached data
  * <AUTHOR>
  */
  public async getInitCall(): Promise<void> {
    if (this.probeOperationService?.getSalesOrderNumberListFromCache().length === 0) {
      this.salesOrderNumberList = await this.probeOperationService.getSalesOrderNumberList();
    } else {
      this.salesOrderNumberList = this.probeOperationService.getSalesOrderNumberListFromCache();
    }

    if (this.probeOperationService.getCountryListFromCache().length === 0) {
      this.countryList = await this.probeOperationService.getCountryList();
    } else {
      this.countryList = this.probeOperationService.getCountryListFromCache();
    }

    if (this.probeOperationService.getProbeTypesListFromCache().length === 0) {
      this.probeTypesList = await this.probeOperationService.getProbeTypesList();
    } else {
      this.probeTypesList = this.probeOperationService.getProbeTypesListFromCache();
    }

    if (this.probeOperationService.getFeaturesListFromCache().length === 0) {
      this.featuresList = await this.probeOperationService.getFeaturesList();
    } else {
      this.featuresList = this.probeOperationService.getFeaturesListFromCache();
    }

    if (this.probeOperationService.getPresetListFromCache().length === 0) {
      this.presetList = await this.probeOperationService.getPresetList();
    } else {
      this.presetList = this.probeOperationService.getPresetListFromCache();
    }
  }

  /**
  * Initialize dropdown data using cached data from probe operation service
  * Uses cached data to avoid unnecessary API calls on filter show/hide
  * <AUTHOR>
  */
  private async initializeDropdownData(): Promise<void> {
    // Use cached data if available, otherwise the getInitCall method will handle API calls
    this.salesOrderNumberList = this.probeOperationService.getSalesOrderNumberListFromCache();
    this.countryList = this.probeOperationService.getCountryListFromCache();
    this.probeTypesList = this.probeOperationService.getProbeTypesListFromCache();
    this.featuresList = this.probeOperationService.getFeaturesListFromCache();
    this.presetList = this.probeOperationService.getPresetListFromCache();
    this.featureValidityPeriodList = this.keyValueMappingServiceService.featureValidityPeriodList();
    this.osTypeList = this.keyValueMappingServiceService.enumOptionToList(OSTypeEnum);
    this.productStatusList = this.keyValueMappingServiceService.enumOptionToList(ProductStatusEnum);
  }

  /**
  * Set Filter value
  *
  * Note : if user hide and show filter then set data in storage
  *
  * <AUTHOR>
  */
  private setFilterValue(): void {
    if (this.probeListFilterRequestBody != null) {
      // Set filter values from the request body if available
      this.filterForm.get('serialNumber').setValue(this.probeListFilterRequestBody.serialNumber);
      this.filterForm.get('customerName').setValue(this.probeListFilterRequestBody.customerName);
      this.filterForm.get('deviceModel').setValue(this.probeListFilterRequestBody.deviceModel);
      this.filterForm.get('manufacturer').setValue(this.probeListFilterRequestBody.manufacturer);
      this.filterForm.get('probeTypes').setValue(this.probeListFilterRequestBody.probeTypes);
      this.filterForm.get('salesOrderNumber').setValue(this.probeListFilterRequestBody.salesOrderNumbers);

      // Map features array to form control
      this.filterForm.get('probeFeatures').setValue(this.probeListFilterRequestBody.features);

      // Map presets array to form control
      this.filterForm.get('presetType').setValue(this.probeListFilterRequestBody.presets);

      // Map feature validity period boolean to array format expected by dropdown
      const validityPeriod = this.probeListFilterRequestBody.featureValidityPeriod !== null ?
        this.featureValidityPeriodList.filter(period => period.value === this.probeListFilterRequestBody.featureValidityPeriod) : [];
      this.filterForm.get('featureValidityPeriod').setValue(validityPeriod);

      // Map OS type enum to array format expected by dropdown
      const osType = this.probeListFilterRequestBody.osType ?
        this.commonsService.getEnumMappingSelectedValue(OSTypeEnum, [this.probeListFilterRequestBody.osType]) : [];
      this.filterForm.get('osType').setValue(osType);

      // Map product status array to form control
      const productStatus = this.probeListFilterRequestBody.productStatus ?
        this.commonsService.getEnumMappingSelectedValue(ProductStatusEnum, this.probeListFilterRequestBody.productStatus) : [];
      this.filterForm.get('productStatus').setValue(productStatus);

      // Map country IDs to country objects
      const countries = this.probeListFilterRequestBody.countryIds?.length ?
        this.countryList.filter(country => this.probeListFilterRequestBody.countryIds.includes(country.id)) : [];
      this.filterForm.get('countries').setValue(countries);

      // Map locked boolean to array format expected by dropdown
      const lockState = this.probeListFilterRequestBody.locked !== null ?
        this.lockUnlockStateList.filter(state => state.value === this.probeListFilterRequestBody.locked) : [];
      this.filterForm.get('lockState').setValue(lockState);

      // Map editable boolean to array format expected by dropdown
      const editState = this.probeListFilterRequestBody.isEditable !== null ?
        this.editStateList.filter(state => state.value === this.probeListFilterRequestBody.isEditable) : [];
      this.filterForm.get('probeEditState').setValue(editState);
    }

    // Handle navigation refresh when returning from detail page
    if (this.listPageRefreshForbackToDetailPage) {
      this.listToModuleConnect.emit(false);
      this.probeListPageRefresh(this.defaultListingPageReloadSubjectParameter);
    }
  }

  /**
  * Reload Listing Data
  * <AUTHOR>
  */
  public searchData(): void {
    const allFormValue = this.filterForm.value;

    const searchProcessed = this.probeOperationService.processFilterSearch(allFormValue, this.filterForm.invalid, this.defaultListingPageReloadSubjectParameter);

    if (!searchProcessed) {
      // Validation failed - message already shown by service
      return;
    }
  }

  /**
  * Search filtered probes
  * <AUTHOR>
  */
  public searchFilteredProbes(): void {
    this.searchData();
  }

  /**
  * Clear Filter
  * <AUTHOR>
  */
  public clearFilter(): void {
    this.clearAllFilter();
    this.probeOperationService.clearAllFiltersAndRefresh(this.defaultListingPageReloadSubjectParameter);
  }

  /**
    * Clear All the filter
    *
    * <AUTHOR>
    */
  public clearAllFilter(): void {
    this.onCountryDeSelect();
    this.filterForm.reset();
    this.filterForm.patchValue({
      salesOrderNumber: [],
      serialNumber: null,
      probeTypes: null,
      probeFeatures: null,
      presetType: null,
      customerName: null,
      deviceModel: null,
      manufacturer: null,
      featureValidityPeriod: null,
      osType: null,
      productStatus: [],
      deviceHistoricalData: null,
      countries: null,
      lockState: null,
      probeEditState: null
    });
  }

  /**
  * Send filter data to listing component and reload page
  * <AUTHOR>
  */
  private probeListPageRefresh(listingPageReloadSubjectParameter: ListingPageReloadSubjectParameter): void {
    if (this.filterForm.invalid) {
      this.filterForm.reset();
    }

    const probeListFilterRequestBody = this.probeOperationService.buildProbeListFilterRequestBody(this.filterForm.value);
    const probeFilterAction = new ProbeFilterAction(listingPageReloadSubjectParameter, probeListFilterRequestBody);
    this.probeOperationService.callProbeListFilterRequestParameterSubject(probeFilterAction);
  }

  /**
  * Set Device Historical Data
  *
  * <AUTHOR>
  * @param event
  */
  public setDeviceHistoricalData(event: any) {
    let value = event.target.checked ? DeviceHistoricalData.HISTOTY : null;
    this.filterForm.get('deviceHistoricalData').setValue(value);
  }

  /**
  * Select single country
  *
  * <AUTHOR>
  * @param item
  */
  public onCountrySelect(item: any): void {
    if (item.id == -1) {
      this.filterForm.get('countries').setValue([item]);
      this.countryList = this.multiSelectDropDownSettingService.setOtherOptionDisabled(this.countryList);
    }
  }

  /**
  * DeSelect Single Country
  *
  * <AUTHOR>
  */
  public onCountryDeSelect(): void {
    this.countryList = this.multiSelectDropDownSettingService.setAllOptionEnable(this.countryList);
  }

  /**
  * Component cleanup
  * <AUTHOR>
  */
  ngOnDestroy(): void {
    if (this.subscriptionForRefreshList) {
      this.subscriptionForRefreshList.unsubscribe();
    }
  }
}
