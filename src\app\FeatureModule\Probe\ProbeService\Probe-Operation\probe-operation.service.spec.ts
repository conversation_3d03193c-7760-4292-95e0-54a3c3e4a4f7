import { HttpResponse } from '@angular/common/http';
import { TestBed } from '@angular/core/testing';
import { ToastrService } from 'ngx-toastr';
import { of, throwError } from 'rxjs';
import { ProbDetailResource, ProbListResource } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { CustomerAssociationRequest } from 'src/app/model/customer-association-request';
import { ConfigureLicenceResponse } from 'src/app/model/probe/ConfigureLicenceResponse.model';
import { ProbeDetailPageResponse } from 'src/app/model/probe/ProbeDetailPageResponse.model';
import { ProbeFilterAction } from 'src/app/model/probe/ProbeFilterAction.model';
import { ProbeListFilterRequestBody } from 'src/app/model/probe/ProbeListFilterRequestBody.model';
import { DeviceService } from 'src/app/shared/device.service';
import { ProbeOperationsEnum } from 'src/app/shared/enum/Operations/ProbeOperations.enum';
import { DeviceHistoricalData } from 'src/app/shared/enum/Probe/DeviceHistoricalData.enum';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CustomerAssociationService } from 'src/app/shared/modalservice/customer-association.service';
import { UpdateProbeTypeService } from 'src/app/shared/modalservice/Probe/update-probe-type.service';
import { UpdateFeaturesService } from 'src/app/shared/modalservice/update-features.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CountryCacheService } from 'src/app/shared/Service/CacheService/countrycache.service';
import { PresetApiService } from 'src/app/shared/Service/PresetService/preset-api.service';
import { ProbeApiService } from 'src/app/shared/Service/ProbeService/probe-api.service';
import { ProbeService } from 'src/app/shared/Service/ProbeService/probe.service';
import { SalesOrderApiCallService } from 'src/app/shared/Service/SalesOrderService/sales-order-api-call.service';
import { CommonsService } from 'src/app/shared/util/commons.service';
import { DownloadService } from 'src/app/shared/util/download.service';
import { KeyValueMappingServiceService } from 'src/app/shared/util/key-value-mapping-service.service';
import { ModuleValidationServiceService } from 'src/app/shared/util/module-validation-service.service';
import { commonsProviders, simulateApiError } from 'src/app/Tesing-Helper/test-utils';
import { ProbeOperationService } from './probe-operation.service';

describe('ProbeOperationService', () => {
  let service: ProbeOperationService;
  let probeApiServiceSpy: jasmine.SpyObj<ProbeApiService>;
  let probeServiceSpy: jasmine.SpyObj<ProbeService>;
  let salesOrderApiCallServiceSpy: jasmine.SpyObj<SalesOrderApiCallService>;
  let countryCacheServiceSpy: jasmine.SpyObj<CountryCacheService>;
  let commonsServiceSpy: jasmine.SpyObj<CommonsService>;
  let exceptionHandlingServiceSpy: jasmine.SpyObj<ExceptionHandlingService>;
  let toastrServiceSpy: jasmine.SpyObj<ToastrService>;
  let downloadServiceSpy: jasmine.SpyObj<DownloadService>;
  let moduleValidationServiceSpy: jasmine.SpyObj<ModuleValidationServiceService>;
  let permissionServiceSpy: jasmine.SpyObj<PermissionService>;
  let confirmDialogServiceSpy: jasmine.SpyObj<ConfirmDialogService>;
  let updateFeaturesServiceSpy: jasmine.SpyObj<UpdateFeaturesService>;
  let customerAssociationServiceSpy: jasmine.SpyObj<CustomerAssociationService>;
  let updateProbeTypeServiceSpy: jasmine.SpyObj<UpdateProbeTypeService>;
  let deviceServiceSpy: jasmine.SpyObj<DeviceService>;

  beforeEach(() => {
    const probeApiSpy = jasmine.createSpyObj('ProbeApiService', [
      'getAllProbes', 'getProbeTypesList', 'getFeaturesList', 'getPresetsList', 'getFilterValue',
      'updateLockState', 'deleteProbes', 'rmaProductStatusForProbe', 'disableProductStatusForProbe',
      'associationProbeWithSalesOrder', 'dowloadSasUriofFeatureLicenseAsync', 'generateCSVFileForProbe',
      'downloadCSVFileForProbe', 'generateCSVFileForProbeHistoricalConnection', 'editEnableDisableProbe'
    ]);
    const probeSpy = jasmine.createSpyObj('ProbeService', [
      'getFeaturesListForFilter', 'getPresetsListForFilter', 'probeEditAction'
    ]);
    const salesOrderSpy = jasmine.createSpyObj('SalesOrderApiCallService', ['getSalesOrderNumberList']);
    const countryCacheSpy = jasmine.createSpyObj('CountryCacheService', ['getCountryListFromCache']);
    const commonsSpy = jasmine.createSpyObj('CommonsService', [
      'checkNullFieldValue', 'getIdsFromArray', 'getSelectedValueFromEnum',
      'getSelectedValueFromBooleanKeyValueMapping'
    ]);
    const exceptionSpy = jasmine.createSpyObj('ExceptionHandlingService', ['customErrorMessage']);
    const toastrSpy = jasmine.createSpyObj('ToastrService', ['info', 'error', 'success']);
    const downloadSpy = jasmine.createSpyObj('DownloadService', ['downloadExportCSV']);
    const moduleValidationSpy = jasmine.createSpyObj('ModuleValidationServiceService', [
      'validateWithEditableWithMultipalRecoard', 'validateWithUserCountryForMultileRecord',
      'validateWithEditStateForSingleRecord', 'validateWithUserCountryForSingleRecord'
    ]);
    const keyValueMappingSpy = jasmine.createSpyObj('KeyValueMappingServiceService', ['enumOptionToList']);
    const permissionSpy = jasmine.createSpyObj('PermissionService', ['getProbPermission']);
    const confirmDialogSpy = jasmine.createSpyObj('ConfirmDialogService', [
      'confirm', 'getBasicModelConfigForDisableAction'
    ]);
    const updateFeaturesSpy = jasmine.createSpyObj('UpdateFeaturesService', [
      'openAssignProbeFeatureModel', 'getAssignProbeBasicModelConfigDetail'
    ]);
    const customerAssociationSpy = jasmine.createSpyObj('CustomerAssociationService', ['openCustomerAssociationPopup']);
    const updateProbeTypeSpy = jasmine.createSpyObj('UpdateProbeTypeService', ['openUpdateProbeTypePopup']);
    const deviceSpy = jasmine.createSpyObj('DeviceService', ['dowloadSasUriofFeatureLicenseConfirmationModel']);
    const presetApiSpy = jasmine.createSpyObj('PresetApiService', ['getPresetsList']);

    TestBed.configureTestingModule({
      imports: [],
      providers: [
        ProbeOperationService,
        { provide: ProbeApiService, useValue: probeApiSpy },
        { provide: ProbeService, useValue: probeSpy },
        { provide: SalesOrderApiCallService, useValue: salesOrderSpy },
        { provide: CountryCacheService, useValue: countryCacheSpy },
        { provide: CommonsService, useValue: commonsSpy },
        { provide: ExceptionHandlingService, useValue: exceptionSpy },
        { provide: DownloadService, useValue: downloadSpy },
        { provide: ModuleValidationServiceService, useValue: moduleValidationSpy },
        { provide: KeyValueMappingServiceService, useValue: keyValueMappingSpy },
        { provide: PermissionService, useValue: permissionSpy },
        { provide: ConfirmDialogService, useValue: confirmDialogSpy },
        { provide: UpdateFeaturesService, useValue: updateFeaturesSpy },
        { provide: CustomerAssociationService, useValue: customerAssociationSpy },
        { provide: UpdateProbeTypeService, useValue: updateProbeTypeSpy },
        { provide: DeviceService, useValue: deviceSpy },
        { provide: PresetApiService, useValue: presetApiSpy },
        commonsProviders(toastrSpy)
      ]
    });

    service = TestBed.inject(ProbeOperationService);
    probeApiServiceSpy = TestBed.inject(ProbeApiService) as jasmine.SpyObj<ProbeApiService>;
    probeServiceSpy = TestBed.inject(ProbeService) as jasmine.SpyObj<ProbeService>;
    salesOrderApiCallServiceSpy = TestBed.inject(SalesOrderApiCallService) as jasmine.SpyObj<SalesOrderApiCallService>;
    countryCacheServiceSpy = TestBed.inject(CountryCacheService) as jasmine.SpyObj<CountryCacheService>;
    commonsServiceSpy = TestBed.inject(CommonsService) as jasmine.SpyObj<CommonsService>;
    exceptionHandlingServiceSpy = TestBed.inject(ExceptionHandlingService) as jasmine.SpyObj<ExceptionHandlingService>;
    toastrServiceSpy = TestBed.inject(ToastrService) as jasmine.SpyObj<ToastrService>;
    downloadServiceSpy = TestBed.inject(DownloadService) as jasmine.SpyObj<DownloadService>;
    moduleValidationServiceSpy = TestBed.inject(ModuleValidationServiceService) as jasmine.SpyObj<ModuleValidationServiceService>;
    permissionServiceSpy = TestBed.inject(PermissionService) as jasmine.SpyObj<PermissionService>;
    confirmDialogServiceSpy = TestBed.inject(ConfirmDialogService) as jasmine.SpyObj<ConfirmDialogService>;
    updateFeaturesServiceSpy = TestBed.inject(UpdateFeaturesService) as jasmine.SpyObj<UpdateFeaturesService>;
    customerAssociationServiceSpy = TestBed.inject(CustomerAssociationService) as jasmine.SpyObj<CustomerAssociationService>;
    updateProbeTypeServiceSpy = TestBed.inject(UpdateProbeTypeService) as jasmine.SpyObj<UpdateProbeTypeService>;
    deviceServiceSpy = TestBed.inject(DeviceService) as jasmine.SpyObj<DeviceService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('Subject Methods', () => {
    it('should return probe list loading subject', () => {
      const subject = service.getProbeListLoadingSubject();
      expect(subject).toBeDefined();
    });

    it('should call probe list loading subject', () => {
      spyOn(service.getProbeListLoadingSubject(), 'next');
      service.callProbeListLoadingSubject(true);
      expect(service.getProbeListLoadingSubject().next).toHaveBeenCalledWith(true);
    });

    it('should return probe detail loading subject', () => {
      const subject = service.getProbeDetailLoadingSubject();
      expect(subject).toBeDefined();
    });

    it('should call probe detail loading subject', () => {
      spyOn(service.getProbeDetailLoadingSubject(), 'next');
      service.callProbeDetailLoadingSubject(false);
      expect(service.getProbeDetailLoadingSubject().next).toHaveBeenCalledWith(false);
    });

    it('should return probe list refresh subject', () => {
      const subject = service.getProbeListRefreshSubject();
      expect(subject).toBeDefined();
    });

    it('should return probe detail refresh subject', () => {
      const subject = service.getProbeDetailRefreshSubject();
      expect(subject).toBeDefined();
    });

    it('should return probe list filter request parameter subject', () => {
      const subject = service.getProbeListFilterRequestParameterSubject();
      expect(subject).toBeDefined();
    });

    it('should call probe list filter request parameter subject', () => {
      const mockFilterAction = new ProbeFilterAction(
        new ListingPageReloadSubjectParameter(true, true, false, false),
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null)
      );
      spyOn(service.getProbeListFilterRequestParameterSubject(), 'next');
      service.callProbeListFilterRequestParameterSubject(mockFilterAction);
      expect(service.getProbeListFilterRequestParameterSubject().next).toHaveBeenCalledWith(mockFilterAction);
    });

    it('should return transfer probe UI subject', () => {
      const subject = service.getTransferProbeUISubject();
      expect(subject).toBeDefined();
    });

    it('should call transfer probe UI subject', () => {
      spyOn(service.getTransferProbeUISubject(), 'next');
      service.callTransferProbeUISubject(true);
      expect(service.getTransferProbeUISubject().next).toHaveBeenCalledWith(true);
    });
  });

  describe('Loading Methods', () => {
    it('should call probe list loading subject for ProbListResource', () => {
      spyOn(service, 'callProbeListLoadingSubject');
      service.isLoading(true, ProbListResource);
      expect(service.callProbeListLoadingSubject).toHaveBeenCalledWith(true);
    });

    it('should call probe detail loading subject for ProbDetailResource', () => {
      spyOn(service, 'callProbeDetailLoadingSubject');
      service.isLoading(false, ProbDetailResource);
      expect(service.callProbeDetailLoadingSubject).toHaveBeenCalledWith(false);
    });

    it('should not call any loading subject for unknown resource', () => {
      spyOn(service, 'callProbeListLoadingSubject');
      spyOn(service, 'callProbeDetailLoadingSubject');
      service.isLoading(true, 'UnknownResource');
      expect(service.callProbeListLoadingSubject).not.toHaveBeenCalled();
      expect(service.callProbeDetailLoadingSubject).not.toHaveBeenCalled();
    });
  });

  describe('Cache Methods', () => {
    it('should set and get sales order number list from cache', () => {
      const mockSalesOrders = ['SO001', 'SO002'];
      service.setSalesOrderNumberList(mockSalesOrders);
      const result = service.getSalesOrderNumberListFromCache();
      expect(result).toEqual(mockSalesOrders);
    });

    it('should get sales order number list from API when cache is empty', async () => {
      const mockSalesOrders = ['SO001', 'SO002'];
      salesOrderApiCallServiceSpy.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));

      const result = await service.getSalesOrderNumberList();
      expect(result).toEqual(mockSalesOrders);
      expect(salesOrderApiCallServiceSpy.getSalesOrderNumberList).toHaveBeenCalled();
    });

    it('should return cached sales order number list on second call', async () => {
      const mockSalesOrders = ['SO001', 'SO002'];
      service.setSalesOrderNumberList(mockSalesOrders);

      const result = await service.getSalesOrderNumberList();
      expect(result).toEqual(mockSalesOrders);
      expect(salesOrderApiCallServiceSpy.getSalesOrderNumberList).not.toHaveBeenCalled();
    });

    it('should handle error when getting sales order numbers from API', async () => {
      const error = new Error('API Error');
      salesOrderApiCallServiceSpy.getSalesOrderNumberList.and.returnValue(Promise.reject(error));

      const result = await service.getSalesOrderNumberList();
      expect(result).toEqual([]);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
    });

    it('should set and get probe types list from cache', () => {
      const mockProbeTypes = ['Type1', 'Type2'];
      service.setProbeTypesList(mockProbeTypes);
      const result = service.getProbeTypesListFromCache();
      expect(result).toEqual(mockProbeTypes);
    });

    it('should get probe types list from API when cache is empty', async () => {
      const mockProbeTypes = ['Type1', 'Type2'];
      probeApiServiceSpy.getProbeTypesList.and.returnValue(of({ body: mockProbeTypes, status: 200 } as any));

      const result = await service.getProbeTypesList();
      expect(result).toEqual(mockProbeTypes);
    });

    it('should handle error when getting probe types from API', async () => {
      probeApiServiceSpy.getProbeTypesList.and.returnValue(simulateApiError());

      const result = await service.getProbeTypesList();
      expect(result).toEqual([]);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
    });

    it('should set and get features list from cache', () => {
      const mockFeatures = [{ id: 1, name: 'Feature1' }] as any;
      service.setFeaturesList(mockFeatures);
      const result = service.getFeaturesListFromCache();
      expect(result).toEqual(mockFeatures);
    });

    it('should get features list from API when cache is empty', async () => {
      const mockFeatures = [{ featureId: 1, displayName: 'Feature1', partNumbers: [] }] as any;
      const mockFilteredFeatures = [{ id: 1, name: 'Feature1', displayName: 'Feature1', isDisabled: false }] as any;
      probeApiServiceSpy.getFeaturesList.and.returnValue(Promise.resolve(mockFeatures));
      probeServiceSpy.getFeaturesListForFilter.and.returnValue(mockFilteredFeatures);

      const result = await service.getFeaturesList();
      expect(result).toEqual(mockFilteredFeatures);
      expect(probeServiceSpy.getFeaturesListForFilter).toHaveBeenCalledWith(mockFeatures, false);
    });

    it('should handle error when getting features from API', async () => {
      const error = new Error('API Error');
      probeApiServiceSpy.getFeaturesList.and.returnValue(Promise.reject(error));

      const result = await service.getFeaturesList();
      expect(result).toEqual([]);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
    });

    it('should set and get preset list from cache', () => {
      const mockPresets = [{ id: 1, name: 'Preset1' }] as any;
      service.setPresetList(mockPresets);
      const result = service.getPresetListFromCache();
      expect(result).toEqual(mockPresets);
    });

    it('should get preset list from API when cache is empty', async () => {
      const mockPresets = [{ id: 1, name: 'Preset1' }] as any;
      const mockFilteredPresets = [{ id: 1, name: 'Preset1' }] as any;
      probeApiServiceSpy.getPresetsList.and.returnValue(Promise.resolve(mockPresets));
      probeServiceSpy.getPresetsListForFilter.and.returnValue(mockFilteredPresets);

      const result = await service.getPresetList();
      expect(result).toEqual(mockFilteredPresets);
    });

    it('should handle error when getting presets from API', async () => {
      const error = new Error('API Error');
      probeApiServiceSpy.getPresetsList.and.returnValue(Promise.reject(error));

      const result = await service.getPresetList();
      expect(result).toEqual([]);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
    });

    it('should set and get country list from cache', () => {
      const mockCountries = [{ id: 1, name: 'Country1' }] as any;
      service.setCountryList(mockCountries);
      const result = service.getCountryListFromCache();
      expect(result).toEqual(mockCountries);
    });

    it('should get country list from API when cache is empty', async () => {
      const mockCountries = [{ id: 1, name: 'Country1' }] as any;
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountries));

      const result = await service.getCountryList();
      expect(result).toEqual(mockCountries);
      expect(countryCacheServiceSpy.getCountryListFromCache).toHaveBeenCalledWith(true);
    });

    it('should handle error when getting countries from API', async () => {
      const error = new Error('API Error');
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.reject(error));

      const result = await service.getCountryList();
      expect(result).toEqual([]);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
    });

    it('should update cache in background', async () => {
      const mockSalesOrders = ['SO001'];
      const mockProbeTypes = ['Type1'];
      const mockFeatures = [{ id: 1 }] as any;
      const mockFilteredFeatures = [{ id: 1 }] as any;
      const mockPresets = [{ id: 1 }] as any;
      const mockFilteredPresets = [{ id: 1 }] as any;
      const mockCountries = [{ id: 1 }] as any;

      salesOrderApiCallServiceSpy.getSalesOrderNumberList.and.returnValue(Promise.resolve(mockSalesOrders));
      probeApiServiceSpy.getProbeTypesList.and.returnValue(of({ body: mockProbeTypes } as any));
      probeApiServiceSpy.getFeaturesList.and.returnValue(Promise.resolve(mockFeatures));
      probeServiceSpy.getFeaturesListForFilter.and.returnValue(mockFilteredFeatures);
      probeApiServiceSpy.getPresetsList.and.returnValue(Promise.resolve(mockPresets));
      probeServiceSpy.getPresetsListForFilter.and.returnValue(mockFilteredPresets);
      countryCacheServiceSpy.getCountryListFromCache.and.returnValue(Promise.resolve(mockCountries));

      await service.updateCacheInBackground();

      expect(service.getSalesOrderNumberListFromCache()).toEqual(mockSalesOrders);
      expect(service.getProbeTypesListFromCache()).toEqual(mockProbeTypes);
      expect(service.getFeaturesListFromCache()).toEqual(mockFilteredFeatures);
      expect(service.getPresetListFromCache()).toEqual(mockFilteredPresets);
      expect(service.getCountryListFromCache()).toEqual(mockCountries);
    });
  });

  describe('Load Probe List', () => {
    it('should load probe list successfully', async () => {
      const mockProbeData = {
        content: [{ id: 1, serialNumber: 'SN001' }],
        numberOfElements: 1,
        totalElements: 1,
        number: 0,
        pageable: {},
        totalPages: 1,
        last: true,
        first: true,
        size: 10,
        sort: {},
        empty: false
      };
      const mockResponse = new HttpResponse<ProbeDetailPageResponse>({ body: mockProbeData as ProbeDetailPageResponse, status: 200 });

      permissionServiceSpy.getProbPermission.and.returnValue(true);
      probeApiServiceSpy.getAllProbes.and.returnValue(of(mockResponse));

      const result = await service.loadProbeList(
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        { page: 1, size: 10 },
        'NORMAL'
      );

      expect(result.success).toBe(true);
      expect(result.totalProbes).toBe(1);
    });

    it('should return empty result when permission is denied', async () => {
      permissionServiceSpy.getProbPermission.and.returnValue(false);

      const result = await service.loadProbeList(
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        { page: 1, size: 10 },
        'NORMAL'
      );

      expect(result.success).toBe(false);
      expect(result.probes).toEqual([]);
      expect(toastrServiceSpy.error).toHaveBeenCalledWith('Insufficient permissions to load probe list');
    });

    it('should handle API error in load probe list', async () => {
      const error = new Error('API Error');
      permissionServiceSpy.getProbPermission.and.returnValue(true);
      probeApiServiceSpy.getAllProbes.and.returnValue(throwError(() => error));

      const result = await service.loadProbeList(
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        { page: 1, size: 10 },
        'NORMAL'
      );

      expect(result.success).toBe(false);
      expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
    });

    it('should return empty result for non-200 status', async () => {
      const mockResponse = new HttpResponse({ body: null, status: 404 });

      permissionServiceSpy.getProbPermission.and.returnValue(true);
      probeApiServiceSpy.getAllProbes.and.returnValue(of(mockResponse));

      const result = await service.loadProbeList(
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null),
        { page: 1, size: 10 },
        'NORMAL'
      );

      expect(result.success).toBe(false);
      expect(result.probes).toEqual([]);
    });
  });

  describe('Filter Methods', () => {
    it('should process filter search successfully', () => {
      const formValue = {
        serialNumber: 'SN001',
        probeTypes: ['Type1'],
        probeFeatures: [{ id: 1 }]
      };

      spyOn(service, 'validateProbeFilterForm').and.returnValue(true);
      spyOn(service, 'buildProbeListFilterRequestBody').and.returnValue(
        new ProbeListFilterRequestBody(null, null, null, null, null, null, null, null, null, null, null, null, null, null)
      );
      spyOn(service, 'callProbeListFilterRequestParameterSubject');

      const result = service.processFilterSearch(
        formValue,
        false,
        new ListingPageReloadSubjectParameter(true, true, false, false)
      );

      expect(result).toBe(true);
      expect(service.validateProbeFilterForm).toHaveBeenCalledWith(formValue);
      expect(service.callProbeListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should return false for invalid form', () => {
      const result = service.processFilterSearch(
        {},
        true,
        new ListingPageReloadSubjectParameter(true, true, false, false)
      );

      expect(result).toBe(false);
    });

    it('should validate filter form with valid data', () => {
      const formValue = {
        serialNumber: 'SN001',
        probeTypes: ['Type1'],
        probeFeatures: [{ id: 1 }]
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(true);
    });

    it('should invalidate filter form with no data', () => {
      const formValue = {
        serialNumber: null,
        probeTypes: null,
        probeFeatures: null
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(false);
      expect(toastrServiceSpy.info).toHaveBeenCalled();
    });

    it('should invalidate filter form with historical data but missing required fields', () => {
      const formValue = {
        deviceHistoricalData: 'HISTORY',
        deviceModel: null,
        manufacturer: null,
        osType: null
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(false);
      expect(toastrServiceSpy.info).toHaveBeenCalled();
    });

    it('should build probe list filter request body correctly', () => {
      const formValue = {
        serialNumber: 'SN001',
        customerName: 'Customer1',
        deviceModel: 'Model1',
        manufacturer: 'Manufacturer1',
        countries: [{ id: 1 }],
        probeFeatures: [{ id: 1 }, { id: 2 }],
        presetType: [{ id: 1 }],
        featureValidityPeriod: ['ACTIVE'],
        productStatus: ['ENABLED'],
        osType: ['WINDOWS'],
        lockState: ['LOCKED'],
        probeEditState: ['ENABLED'],
        probeTypes: ['Type1'],
        salesOrderNumber: ['SO001']
      };

      commonsServiceSpy.checkNullFieldValue.and.returnValue('test');
      commonsServiceSpy.getIdsFromArray.and.returnValue([1]);
      commonsServiceSpy.getSelectedValueFromEnum.and.returnValue(['ENABLED']);
      commonsServiceSpy.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(true);
      probeApiServiceSpy.getFilterValue.and.returnValue(['filtered']);

      const result = service.buildProbeListFilterRequestBody(formValue);

      expect(commonsServiceSpy.checkNullFieldValue).toHaveBeenCalledWith('SN001');
      expect(commonsServiceSpy.getIdsFromArray).toHaveBeenCalled();
      expect(result).toBeInstanceOf(ProbeListFilterRequestBody);
    });

    it('should handle null feature list in buildProbeListFilterRequestBody', () => {
      const formValue = {
        probeFeatures: null
      };

      commonsServiceSpy.checkNullFieldValue.and.returnValue(null);
      commonsServiceSpy.getIdsFromArray.and.returnValue(null);
      commonsServiceSpy.getSelectedValueFromEnum.and.returnValue([]);
      commonsServiceSpy.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
      probeApiServiceSpy.getFilterValue.and.returnValue(null);

      const result = service.buildProbeListFilterRequestBody(formValue);

      expect(result).toBeInstanceOf(ProbeListFilterRequestBody);
    });

    it('should clear all filters and refresh', () => {
      spyOn(service, 'callProbeListFilterRequestParameterSubject');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, true, false);
      service.clearAllFiltersAndRefresh(listingPageReloadSubjectParameter);

      expect(service.callProbeListFilterRequestParameterSubject).toHaveBeenCalled();
    });
  });

  describe('Refresh Methods', () => {
    it('should call refresh page subject for ProbListResource', () => {
      spyOn(service, 'callProbeListFilterRequestParameterSubject');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      service.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbListResource, false, null);

      expect(service.callProbeListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should call refresh page subject for ProbListResource with existing filter', () => {
      spyOn(service, 'callProbeListFilterRequestParameterSubject');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      const existingFilter = new ProbeListFilterRequestBody('SN001', null, null, null, null, null, null, null, null, null, null, null, null, null);

      service.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbListResource, false, existingFilter);

      expect(service.callProbeListFilterRequestParameterSubject).toHaveBeenCalled();
    });

    it('should call refresh page subject for ProbDetailResource', () => {
      spyOn(service.getProbeDetailRefreshSubject(), 'next');

      const listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, true, false, false);
      service.callRefreshPageSubject(listingPageReloadSubjectParameter, ProbDetailResource, false, null);

      expect(service.getProbeDetailRefreshSubject().next).toHaveBeenCalledWith(listingPageReloadSubjectParameter);
    });

    it('should filter page subject call for reload page', () => {
      spyOn(service, 'callRefreshPageSubject');

      service.filterPageSubjectCallForReloadPage(true, false);

      expect(service.callRefreshPageSubject).toHaveBeenCalled();
    });
  });

  describe('Probe Operations', () => {
    const mockProbeIds = [1, 2, 3];
    const mockSelectedProbes = [
      { id: 1, editable: true, country: 'US', locked: false },
      { id: 2, editable: true, country: 'US', locked: false },
      { id: 3, editable: true, country: 'US', locked: false }
    ];

    beforeEach(() => {
      // Setup default spy returns
      moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForSingleRecord.and.returnValue(true);
      permissionServiceSpy.getProbPermission.and.returnValue(true);
      confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(true));
      confirmDialogServiceSpy.getBasicModelConfigForDisableAction.and.returnValue({
        title: 'Confirm', message: 'Are you sure?', btnOkText: 'OK', btnCancelText: 'Cancel'
      });
    });

    describe('lockUnlockProbes', () => {
      it('should successfully lock probes for list resource', async () => {
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.resolve(true));
        spyOn(service, 'isLoading');
        spyOn(service, 'callRefreshPageSubject');

        await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(probeApiServiceSpy.updateLockState).toHaveBeenCalledWith(mockProbeIds, true);
        expect(service.callRefreshPageSubject).toHaveBeenCalled();
      });

      it('should successfully unlock probes for detail resource', async () => {
        const singleProbe = [{ ...mockSelectedProbes[0], locked: true }];
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.resolve(true));
        spyOn(service, 'isLoading');
        spyOn(service, 'callRefreshPageSubject');

        await service.lockUnlockProbes([1], singleProbe, false, ProbDetailResource);

        expect(probeApiServiceSpy.updateLockState).toHaveBeenCalledWith([1], false);
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(probeApiServiceSpy.updateLockState).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(probeApiServiceSpy.updateLockState).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        const error = new Error('API Error');
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.reject(error));

        await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });

      it('should return false when API returns false', async () => {
        probeApiServiceSpy.updateLockState.and.returnValue(Promise.resolve(false));

        await service.lockUnlockProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

      });

      it('should validate probe lock state for detail resource', async () => {
        const lockedProbe = [{ ...mockSelectedProbes[0], locked: true }];
        spyOn(service, 'isLoading');

        await service.lockUnlockProbes([1], lockedProbe, true, ProbDetailResource);

        expect(toastrServiceSpy.info).toHaveBeenCalled();
      });
    });

    describe('enableDisableProbes', () => {
      it('should successfully enable probes for list resource', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes enabled successfully' } });
        probeApiServiceSpy.editEnableDisableProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(probeApiServiceSpy.editEnableDisableProbe).toHaveBeenCalledWith(mockProbeIds, true);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes enabled successfully', ProbListResource);
      });

      it('should successfully disable probes for detail resource', async () => {
        const enabledProbe = [{ ...mockSelectedProbes[0], editable: true }];
        const mockResponse = new HttpResponse({ body: { message: 'Probes disabled successfully' } });
        probeApiServiceSpy.editEnableDisableProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        await service.enableDisableProbes([1], enabledProbe, false, ProbDetailResource);

        expect(probeApiServiceSpy.editEnableDisableProbe).toHaveBeenCalledWith([1], false);
      });

      it('should return false when validation fails for list resource', async () => {
        await service.enableDisableProbes([], mockSelectedProbes, true, ProbListResource);

        expect(toastrServiceSpy.info).toHaveBeenCalled();
        expect(probeApiServiceSpy.editEnableDisableProbe).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(probeApiServiceSpy.editEnableDisableProbe).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        probeApiServiceSpy.editEnableDisableProbe.and.returnValue(simulateApiError());

        await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });

      it('should validate probe edit state for detail resource', async () => {
        const enabledProbe = [{ ...mockSelectedProbes[0], editable: true }];
        spyOn(service, 'isLoading');

        await service.enableDisableProbes([1], enabledProbe, true, ProbDetailResource);

        expect(toastrServiceSpy.info).toHaveBeenCalled();
      });

      it('should validate user country access for list resource', async () => {
        moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(false);

        await service.enableDisableProbes(mockProbeIds, mockSelectedProbes, true, ProbListResource);
      });
    });

    describe('deleteProbes', () => {
      it('should successfully delete probes', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes deleted successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.deleteProbes.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');
        await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(confirmDialogServiceSpy.confirm).toHaveBeenCalled();
        expect(probeApiServiceSpy.deleteProbes).toHaveBeenCalledWith(mockProbeIds);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes deleted successfully', ProbListResource);
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(confirmDialogServiceSpy.confirm).not.toHaveBeenCalled();
      });

      it('should return false when user cancels confirmation', async () => {
        confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));

        await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(probeApiServiceSpy.deleteProbes).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);
      });

      it('should handle API errors', async () => {
        probeApiServiceSpy.deleteProbes.and.returnValue(simulateApiError());

        await service.deleteProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('rmaProductStatusForProbes', () => {
      it('should successfully set probes to RMA status for list resource', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes set to RMA successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.rmaProductStatusForProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        await service.rmaProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(confirmDialogServiceSpy.confirm).toHaveBeenCalled();
        expect(probeApiServiceSpy.rmaProductStatusForProbe).toHaveBeenCalledWith(mockProbeIds);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes set to RMA successfully', ProbListResource);
      });

      it('should successfully set probes to RMA status for detail resource', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes set to RMA successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.rmaProductStatusForProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        await service.rmaProductStatusForProbes([1], [mockSelectedProbes[0]], ProbDetailResource);
      });

      it('should return false when validation fails for list resource', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        await service.rmaProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(confirmDialogServiceSpy.confirm).not.toHaveBeenCalled();
      });

      it('should return false when validation fails for detail resource', async () => {
        moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(false);

        await service.rmaProductStatusForProbes([1], [mockSelectedProbes[0]], ProbDetailResource);
      });

      it('should return false when user cancels confirmation', async () => {
        confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));

        await service.rmaProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(probeApiServiceSpy.rmaProductStatusForProbe).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        probeApiServiceSpy.rmaProductStatusForProbe.and.returnValue(simulateApiError());

        await service.rmaProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('disableProductStatusForProbes', () => {
      it('should successfully disable probes', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes disabled successfully' } as SuccessMessageResponse });
        probeApiServiceSpy.disableProductStatusForProbe.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(confirmDialogServiceSpy.confirm).toHaveBeenCalled();
        expect(probeApiServiceSpy.disableProductStatusForProbe).toHaveBeenCalledWith(mockProbeIds);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes disabled successfully', ProbListResource);
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(confirmDialogServiceSpy.confirm).not.toHaveBeenCalled();
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

      });

      it('should return false when user cancels confirmation', async () => {
        confirmDialogServiceSpy.confirm.and.returnValue(Promise.resolve(false));

        await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(probeApiServiceSpy.disableProductStatusForProbe).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        probeApiServiceSpy.disableProductStatusForProbe.and.returnValue(simulateApiError());

        await service.disableProductStatusForProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('associationProbeWithSalesOrder', () => {
      it('should successfully associate probes with sales order', async () => {
        const mockResponse = new HttpResponse({ body: { message: 'Probes associated successfully' } as SuccessMessageResponse });
        const mockCustomerAssociationRequest = {
          button: true,
          basicSalesOrderDetailResponse: { id: 1 } as any,
          isSalesOrderNewAdd: false
        } as CustomerAssociationRequest;

        customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve(mockCustomerAssociationRequest));
        probeApiServiceSpy.associationProbeWithSalesOrder.and.returnValue(of(mockResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');

        await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(customerAssociationServiceSpy.openCustomerAssociationPopup).toHaveBeenCalled();
        expect(probeApiServiceSpy.associationProbeWithSalesOrder).toHaveBeenCalledWith(mockProbeIds, jasmine.any(Object));
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('Probes associated successfully', ProbListResource);
      });

      it('should return false when user cancels association', async () => {
        const mockCustomerAssociationRequest = { button: false } as CustomerAssociationRequest;
        customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve(mockCustomerAssociationRequest));

        await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(probeApiServiceSpy.associationProbeWithSalesOrder).not.toHaveBeenCalled();
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(customerAssociationServiceSpy.openCustomerAssociationPopup).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

      });

      it('should handle API errors', async () => {
        const mockCustomerAssociationRequest = {
          button: true,
          basicSalesOrderDetailResponse: { id: 1 } as any
        } as CustomerAssociationRequest;

        customerAssociationServiceSpy.openCustomerAssociationPopup.and.returnValue(Promise.resolve(mockCustomerAssociationRequest));
        probeApiServiceSpy.associationProbeWithSalesOrder.and.returnValue(simulateApiError());

        await service.associationProbeWithSalesOrder(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('updateProbeFeatures', () => {
      it('should successfully update probe features for single probe', async () => {
        const mockConfigureLicenceResponse = { button: true } as ConfigureLicenceResponse;
        updateFeaturesServiceSpy.getAssignProbeBasicModelConfigDetail.and.returnValue({
          title: 'Test', message: 'Test', btnOkText: 'OK', btnCancelText: 'Cancel'
        });
        updateFeaturesServiceSpy.openAssignProbeFeatureModel.and.returnValue(Promise.resolve(mockConfigureLicenceResponse));
        spyOn(service, 'isLoading');
        spyOn(service, 'callRefreshPageSubject');

        await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

        expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).toHaveBeenCalled();
        expect(service.callRefreshPageSubject).toHaveBeenCalled();
      });

      it('should return false when multiple probes are selected', async () => {
        await service.updateProbeFeatures(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(toastrServiceSpy.info).toHaveBeenCalled();
        expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).not.toHaveBeenCalled();
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

        expect(updateFeaturesServiceSpy.openAssignProbeFeatureModel).not.toHaveBeenCalled();
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);
        await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

      });

      it('should return false when user cancels feature update', async () => {
        const mockConfigureLicenceResponse = { button: false } as ConfigureLicenceResponse;
        updateFeaturesServiceSpy.getAssignProbeBasicModelConfigDetail.and.returnValue({
          title: 'Test', message: 'Test', btnOkText: 'OK', btnCancelText: 'Cancel'
        });
        updateFeaturesServiceSpy.openAssignProbeFeatureModel.and.returnValue(Promise.resolve(mockConfigureLicenceResponse));

        await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

      });

      it('should handle API errors', async () => {
        const error = new Error('API Error');
        updateFeaturesServiceSpy.getAssignProbeBasicModelConfigDetail.and.returnValue({
          title: 'Test', message: 'Test', btnOkText: 'OK', btnCancelText: 'Cancel'
        });
        updateFeaturesServiceSpy.openAssignProbeFeatureModel.and.returnValue(Promise.reject(error));

        await service.updateProbeFeatures([1], [mockSelectedProbes[0]], ProbListResource);

        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });
  });

  describe('Validation Methods', () => {
    const mockProbeIds = [1, 2, 3];
    const mockSelectedProbes = [
      { id: 1, editable: true, country: 'US', locked: false },
      { id: 2, editable: true, country: 'US', locked: false },
      { id: 3, editable: true, country: 'US', locked: false }
    ];

    beforeEach(() => {
      moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForSingleRecord.and.returnValue(true);
    });

    it('should validate probe permissions for list resource', () => {
      const result = service.validateProbePermissions(mockProbeIds, mockSelectedProbes, ProbListResource);
      expect(result).toBe(true);
    });

    it('should return false when country validation fails for multiple probes', () => {
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(false);

      const result = service.validateProbePermissions(mockProbeIds, mockSelectedProbes, ProbListResource);
      expect(result).toBe(false);
    });
  });

  describe('Additional Probe Operations', () => {
    const mockProbeIds = [1, 2, 3];
    const mockSelectedProbes = [
      { id: 1, editable: true, country: 'US', locked: false, serialNumber: 'SN001' },
      { id: 2, editable: true, country: 'US', locked: false, serialNumber: 'SN002' },
      { id: 3, editable: true, country: 'US', locked: false, serialNumber: 'SN003' }
    ];

    beforeEach(() => {
      moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForMultileRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(true);
      moduleValidationServiceSpy.validateWithUserCountryForSingleRecord.and.returnValue(true);
      permissionServiceSpy.getProbPermission.and.returnValue(true);
    });

    describe('downloadProbes', () => {
      it('should successfully download probes for list resource', async () => {
        const result = await service.downloadProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect(deviceServiceSpy.dowloadSasUriofFeatureLicenseConfirmationModel).toHaveBeenCalledWith(ProbListResource);
      });

      it('should successfully download probes for detail resource', async () => {
        probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync.and.returnValue(Promise.resolve());

        const result = await service.downloadProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(true);
        expect(probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync).toHaveBeenCalledWith([1], ProbDetailResource);
      });

      it('should handle API errors', async () => {
        const error = new Error('API Error');
        probeApiServiceSpy.dowloadSasUriofFeatureLicenseAsync.and.returnValue(Promise.reject(error));

        const result = await service.downloadProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('updateProbeType', () => {
      it('should successfully update probe type', async () => {
        updateProbeTypeServiceSpy.openUpdateProbeTypePopup.and.returnValue(Promise.resolve(true));
        spyOn(service, 'isLoading');
        spyOn(service, 'callRefreshPageSubject');

        const result = await service.updateProbeType(mockProbeIds, mockSelectedProbes, 'TestType', ProbListResource);

        expect(result).toBe(true);
        expect(updateProbeTypeServiceSpy.openUpdateProbeTypePopup).toHaveBeenCalled();
        expect(service.callRefreshPageSubject).toHaveBeenCalled();
      });

      it('should return false when user cancels update', async () => {
        updateProbeTypeServiceSpy.openUpdateProbeTypePopup.and.returnValue(Promise.resolve(false));

        const result = await service.updateProbeType(mockProbeIds, mockSelectedProbes, null, ProbListResource);

        expect(result).toBe(false);
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditableWithMultipalRecoard.and.returnValue(false);

        const result = await service.updateProbeType(mockProbeIds, mockSelectedProbes, 'TestType', ProbListResource);

        expect(result).toBe(false);
        expect(updateProbeTypeServiceSpy.openUpdateProbeTypePopup).not.toHaveBeenCalled();
      });

      it('should handle API errors', async () => {
        const error = new Error('API Error');
        updateProbeTypeServiceSpy.openUpdateProbeTypePopup.and.returnValue(Promise.reject(error));

        const result = await service.updateProbeType(mockProbeIds, mockSelectedProbes, 'TestType', ProbListResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('exportProbesCSV', () => {
      it('should successfully export probes to CSV', async () => {
        const mockGenerateResponse = new HttpResponse({ body: { fileName: 'test.csv' } });
        const mockDownloadResponse = new HttpResponse({ body: new Blob(['test data']), status: 200 });

        probeApiServiceSpy.generateCSVFileForProbe.and.returnValue(of(mockGenerateResponse));
        probeApiServiceSpy.downloadCSVFileForProbe.and.returnValue(of(mockDownloadResponse));
        spyOn(service, 'isLoading');
        spyOn(service as any, 'showSuccessAndRefresh');
        spyOn(service as any, 'getParameterForDeviceHistoricalData').and.returnValue('NORMAL');

        const result = await service.exportProbesCSV(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(true);
        expect(probeApiServiceSpy.generateCSVFileForProbe).toHaveBeenCalled();
        expect(probeApiServiceSpy.downloadCSVFileForProbe).toHaveBeenCalledWith('test.csv');
        expect(downloadServiceSpy.downloadExportCSV).toHaveBeenCalledWith("List_of_Probe(s).xls", mockDownloadResponse);
        expect((service as any).showSuccessAndRefresh).toHaveBeenCalledWith('CSV exported successfully', ProbListResource);
      });

      it('should handle API errors during generation', async () => {
        probeApiServiceSpy.generateCSVFileForProbe.and.returnValue(simulateApiError());
        spyOn(service, 'isLoading');

        const result = await service.exportProbesCSV(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });

      it('should handle API errors during download', async () => {
        const mockGenerateResponse = new HttpResponse({ body: { fileName: 'test.csv' } });

        probeApiServiceSpy.generateCSVFileForProbe.and.returnValue(of(mockGenerateResponse));
        probeApiServiceSpy.downloadCSVFileForProbe.and.returnValue(simulateApiError());
        spyOn(service, 'isLoading');

        const result = await service.exportProbesCSV(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('exportProbeHistoricalConnections', () => {
      it('should successfully export historical connections', async () => {
        const mockGenerateResponse = new HttpResponse({ body: { fileName: 'history.csv' } });
        const mockDownloadResponse = new HttpResponse({ body: new Blob(['history data']), status: 200 });

        probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection.and.returnValue(of(mockGenerateResponse));
        probeApiServiceSpy.downloadCSVFileForProbe.and.returnValue(of(mockDownloadResponse));
        spyOn(service, 'isLoading');

        const result = await service.exportProbeHistoricalConnections(1, 'TEST123', ProbDetailResource);

        expect(result).toBe(true);
        expect(probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection).toHaveBeenCalled();
        expect(downloadServiceSpy.downloadExportCSV).toHaveBeenCalledWith('TEST123_Probe_Connection_History.xls', mockDownloadResponse);
      });

      it('should handle API errors during generation', async () => {
        probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection.and.returnValue(simulateApiError());
        spyOn(service, 'isLoading');

        const result = await service.exportProbeHistoricalConnections(1, 'TEST123', ProbDetailResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });

      it('should handle API errors during download', async () => {
        const mockGenerateResponse = new HttpResponse({ body: { fileName: 'history.csv' } });

        probeApiServiceSpy.generateCSVFileForProbeHistoricalConnection.and.returnValue(of(mockGenerateResponse));
        probeApiServiceSpy.downloadCSVFileForProbe.and.returnValue(simulateApiError());
        spyOn(service, 'isLoading');

        const result = await service.exportProbeHistoricalConnections(1, 'TEST123', ProbDetailResource);

        expect(result).toBe(false);
        expect(exceptionHandlingServiceSpy.customErrorMessage).toHaveBeenCalled();
      });
    });

    describe('transferProbes', () => {
      it('should successfully initiate probe transfer for detail resource', async () => {
        spyOn(service, 'callTransferProbeUISubject');

        const result = await service.transferProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(true);
        expect(service.callTransferProbeUISubject).toHaveBeenCalledWith(true);
      });

      it('should return false for non-detail resource', async () => {
        const result = await service.transferProbes(mockProbeIds, mockSelectedProbes, ProbListResource);

        expect(result).toBe(false);
      });

      it('should return false when validation fails', async () => {
        moduleValidationServiceSpy.validateWithEditStateForSingleRecord.and.returnValue(false);

        const result = await service.transferProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(false);
      });

      it('should return false when permission check fails', async () => {
        permissionServiceSpy.getProbPermission.and.returnValue(false);

        const result = await service.transferProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(false);
      });

      it('should handle errors during transfer', async () => {
        const error = new Error("Transfer failed");
        // Simulate an error condition
        spyOn(service, 'callTransferProbeUISubject').and.throwError(error);

        const result = await service.transferProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(false);
        expect(toastrServiceSpy.error).toHaveBeenCalledWith('Transfer probe operation failed');
      });

      it('should handle errors without specific message', async () => {
        const error = new Error();
        spyOn(service, 'callTransferProbeUISubject').and.throwError(error);

        const result = await service.transferProbes([1], [mockSelectedProbes[0]], ProbDetailResource);

        expect(result).toBe(false);
        expect(toastrServiceSpy.error).toHaveBeenCalledWith('Transfer probe operation failed');
      });
    });
  });

  describe('changeOperationForProbe', () => {
    const mockProbeIds = [1, 2, 3];
    const mockSelectedProbes = [
      { id: 1, editable: true, country: 'US', locked: false, serialNumber: 'SN001', type: 'Type1' },
      { id: 2, editable: true, country: 'US', locked: false, serialNumber: 'SN002', type: 'Type1' },
      { id: 3, editable: true, country: 'US', locked: false, serialNumber: 'SN003', type: 'Type1' }
    ];

    beforeEach(() => {
      spyOn(service, 'lockUnlockProbes').and.returnValue(Promise.resolve());
      spyOn(service, 'enableDisableProbes').and.returnValue(Promise.resolve());
      spyOn(service, 'deleteProbes').and.returnValue(Promise.resolve());
      spyOn(service, 'rmaProductStatusForProbes').and.returnValue(Promise.resolve());
      spyOn(service, 'disableProductStatusForProbes').and.returnValue(Promise.resolve());
      spyOn(service, 'associationProbeWithSalesOrder').and.returnValue(Promise.resolve());
      spyOn(service, 'updateProbeFeatures').and.returnValue(Promise.resolve());
      spyOn(service, 'downloadProbes').and.returnValue(Promise.resolve(true));
      spyOn(service, 'updateProbeType').and.returnValue(Promise.resolve(true));
      spyOn(service, 'exportProbesCSV').and.returnValue(Promise.resolve(true));
      spyOn(service, 'exportProbeHistoricalConnections').and.returnValue(Promise.resolve(true));
      spyOn(service, 'transferProbes').and.returnValue(Promise.resolve(true));
    });

    it('should show info message when no probes selected', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.UNLOCK_PROBES, ProbListResource, [], []);
      expect(toastrServiceSpy.info).toHaveBeenCalled();
    });

    it('should call lockUnlockProbes for UNLOCK_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.UNLOCK_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.lockUnlockProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, false, ProbListResource);
    });

    it('should call lockUnlockProbes for LOCK_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.LOCK_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.lockUnlockProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, true, ProbListResource);
    });

    it('should call enableDisableProbes for EDIT_ENABLE_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EDIT_ENABLE_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.enableDisableProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, true, ProbListResource);
    });

    it('should call enableDisableProbes for EDIT_DISABLE_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EDIT_DISABLE_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.enableDisableProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, false, ProbListResource);
    });

    it('should call deleteProbes for DELETE_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.DELETE_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.deleteProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call rmaProductStatusForProbes for RMA_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.RMA_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.rmaProductStatusForProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call disableProductStatusForProbes for DISABLED_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.DISABLED_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.disableProductStatusForProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call associationProbeWithSalesOrder for CUSTOMER_ASSOCIATION operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.CUSTOMER_ASSOCIATION, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.associationProbeWithSalesOrder).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call updateProbeFeatures for ASSIGN_FEATURES_TO_PROBE operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.ASSIGN_FEATURES_TO_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.updateProbeFeatures).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call downloadProbes for DOWNLOAD_PROBES operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.DOWNLOAD_PROBES, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.downloadProbes).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call updateProbeType for UPDATE_PROBE_TYPE operation with list resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.UPDATE_PROBE_TYPE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.updateProbeType).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, null, ProbListResource);
    });

    it('should call updateProbeType for UPDATE_PROBE_TYPE operation with detail resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.UPDATE_PROBE_TYPE, ProbDetailResource, [1], [mockSelectedProbes[0]]);
      expect(service.updateProbeType).toHaveBeenCalledWith([1], [mockSelectedProbes[0]], 'Type1', ProbDetailResource);
    });

    it('should call exportProbesCSV for Export_CSV operation', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.Export_CSV, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.exportProbesCSV).toHaveBeenCalledWith(mockProbeIds, mockSelectedProbes, ProbListResource);
    });

    it('should call exportProbeHistoricalConnections for EXPORT_HISTORICAL_CONNECTIONS operation with detail resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS, ProbDetailResource, [1], [mockSelectedProbes[0]]);
      expect(service.exportProbeHistoricalConnections).toHaveBeenCalledWith(1, 'SN001', ProbDetailResource);
    });

    it('should handle exportProbeHistoricalConnections with Unknown serial number', () => {
      const probeWithoutSerial = [{ ...mockSelectedProbes[0], serialNumber: null }];
      service.changeOperationForProbe(ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS, ProbDetailResource, [1], probeWithoutSerial);
      expect(service.exportProbeHistoricalConnections).toHaveBeenCalledWith(1, 'Unknown', ProbDetailResource);
    });

    it('should not call exportProbeHistoricalConnections for non-detail resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.EXPORT_HISTORICAL_CONNECTIONS, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.exportProbeHistoricalConnections).not.toHaveBeenCalled();
    });

    it('should call transferProbes for TRANSFER_PROBE operation with detail resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.TRANSFER_PROBE, ProbDetailResource, [1], [mockSelectedProbes[0]]);
      expect(service.transferProbes).toHaveBeenCalledWith([1], [mockSelectedProbes[0]], ProbDetailResource);
    });

    it('should not call transferProbes for non-detail resource', () => {
      service.changeOperationForProbe(ProbeOperationsEnum.TRANSFER_PROBE, ProbListResource, mockProbeIds, mockSelectedProbes);
      expect(service.transferProbes).not.toHaveBeenCalled();
    });

    it('should handle unknown operation gracefully', () => {
      service.changeOperationForProbe('UNKNOWN_OPERATION', ProbListResource, mockProbeIds, mockSelectedProbes);
      // Should not call any operation methods
      expect(service.lockUnlockProbes).not.toHaveBeenCalled();
      expect(service.enableDisableProbes).not.toHaveBeenCalled();
    });
  });

  describe('Private Methods Coverage', () => {
    it('should test private showSuccessAndRefresh method', () => {
      spyOn(service, 'callRefreshPageSubject');

      // Access private method through type assertion
      (service as any).showSuccessAndRefresh('Test message', ProbListResource);

      expect(toastrServiceSpy.success).toHaveBeenCalledWith('Test message');
      expect(service.callRefreshPageSubject).toHaveBeenCalled();
    });

    it('should test private getParameterForDeviceHistoricalData method', () => {
      const result = (service as any).getParameterForDeviceHistoricalData();
      expect(result).toBe(DeviceHistoricalData.HISTOTY);
    });

    it('should extract feature IDs from feature objects', () => {
      const featuresList = [{ id: 1 }, { id: 2 }, { id: 3 }];
      const result = (service as any).getFeatureIds(featuresList);
      expect(result).toEqual([1, 2, 3]);
    });

    it('should return null for empty feature list', () => {
      const result = (service as any).getFeatureIds([]);
      expect(result).toBeNull();
    });

    it('should return null for null feature list', () => {
      const result = (service as any).getFeatureIds(null);
      expect(result).toBeNull();
    });

    it('should get probe associated countries', () => {
      const selectedProbes = [
        { country: 'US' },
        { country: 'CA' },
        { country: 'UK' }
      ];
      const result = (service as any).getProbeAssociatedCountries(selectedProbes);
      expect(result).toEqual(['US', 'CA', 'UK']);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle null responses in cache methods', async () => {
      probeApiServiceSpy.getProbeTypesList.and.returnValue(of({ body: null, status: 200 } as any));

      const result = await service.getProbeTypesList();
      expect(result).toEqual([]);
    });

    it('should handle undefined responses in cache methods', async () => {
      probeApiServiceSpy.getProbeTypesList.and.returnValue(of({ status: 200 } as any));

      const result = await service.getProbeTypesList();
      expect(result).toEqual([]);
    });

    it('should handle filter validation with mixed valid/invalid data', () => {
      const formValue = {
        serialNumber: 'SN001',
        deviceHistoricalData: 'HISTORY',
        deviceModel: null, // This should cause validation failure
        manufacturer: null,
        osType: null
      };

      const result = service.validateProbeFilterForm(formValue);
      expect(result).toBe(false);
      expect(toastrServiceSpy.info).toHaveBeenCalled();
    });

    it('should handle buildProbeListFilterRequestBody with edge case values', () => {
      const formValue = {
        countries: [],
        probeFeatures: [],
        presetType: [],
        featureValidityPeriod: [],
        productStatus: [],
        osType: [],
        lockState: [],
        probeEditState: []
      };

      commonsServiceSpy.checkNullFieldValue.and.returnValue(null);
      commonsServiceSpy.getIdsFromArray.and.returnValue([]);
      commonsServiceSpy.getSelectedValueFromEnum.and.returnValue([]);
      commonsServiceSpy.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
      probeApiServiceSpy.getFilterValue.and.returnValue(null);

      const result = service.buildProbeListFilterRequestBody(formValue);
      expect(result).toBeInstanceOf(ProbeListFilterRequestBody);
    });

    it('should handle single item arrays in buildProbeListFilterRequestBody', () => {
      const formValue = {
        featureValidityPeriod: [{ value: 'ACTIVE' }],
        osType: ['WINDOWS']
      };

      commonsServiceSpy.checkNullFieldValue.and.returnValue(null);
      commonsServiceSpy.getIdsFromArray.and.returnValue([]);
      commonsServiceSpy.getSelectedValueFromEnum.and.returnValue(['WINDOWS']);
      commonsServiceSpy.getSelectedValueFromBooleanKeyValueMapping.and.returnValue(null);
      probeApiServiceSpy.getFilterValue.and.returnValue([{ value: 'ACTIVE' }]);

      const result = service.buildProbeListFilterRequestBody(formValue);
      expect(result).toBeInstanceOf(ProbeListFilterRequestBody);
    });
  });

  describe('Complete Method Coverage', () => {
    it('should test all public methods exist and are callable', () => {
      // Test that all public methods exist
      expect(typeof service.getProbeListLoadingSubject).toBe('function');
      expect(typeof service.callProbeListLoadingSubject).toBe('function');
      expect(typeof service.getProbeDetailLoadingSubject).toBe('function');
      expect(typeof service.callProbeDetailLoadingSubject).toBe('function');
      expect(typeof service.getProbeListRefreshSubject).toBe('function');
      expect(typeof service.getProbeDetailRefreshSubject).toBe('function');
      expect(typeof service.getProbeListFilterRequestParameterSubject).toBe('function');
      expect(typeof service.callProbeListFilterRequestParameterSubject).toBe('function');
      expect(typeof service.getTransferProbeUISubject).toBe('function');
      expect(typeof service.callTransferProbeUISubject).toBe('function');
      expect(typeof service.isLoading).toBe('function');
      expect(typeof service.callRefreshPageSubject).toBe('function');
      expect(typeof service.filterPageSubjectCallForReloadPage).toBe('function');
      expect(typeof service.getSalesOrderNumberListFromCache).toBe('function');
      expect(typeof service.getCountryListFromCache).toBe('function');
      expect(typeof service.getProbeTypesListFromCache).toBe('function');
      expect(typeof service.getFeaturesListFromCache).toBe('function');
      expect(typeof service.getPresetListFromCache).toBe('function');
      expect(typeof service.setSalesOrderNumberList).toBe('function');
      expect(typeof service.getSalesOrderNumberList).toBe('function');
      expect(typeof service.setProbeTypesList).toBe('function');
      expect(typeof service.getProbeTypesList).toBe('function');
      expect(typeof service.setFeaturesList).toBe('function');
      expect(typeof service.getFeaturesList).toBe('function');
      expect(typeof service.setPresetList).toBe('function');
      expect(typeof service.getPresetList).toBe('function');
      expect(typeof service.setCountryList).toBe('function');
      expect(typeof service.getCountryList).toBe('function');
      expect(typeof service.updateCacheInBackground).toBe('function');
      expect(typeof service.loadProbeList).toBe('function');
      expect(typeof service.processFilterSearch).toBe('function');
      expect(typeof service.validateProbeFilterForm).toBe('function');
      expect(typeof service.buildProbeListFilterRequestBody).toBe('function');
      expect(typeof service.clearAllFiltersAndRefresh).toBe('function');
      expect(typeof service.lockUnlockProbes).toBe('function');
      expect(typeof service.enableDisableProbes).toBe('function');
      expect(typeof service.deleteProbes).toBe('function');
      expect(typeof service.rmaProductStatusForProbes).toBe('function');
      expect(typeof service.disableProductStatusForProbes).toBe('function');
      expect(typeof service.associationProbeWithSalesOrder).toBe('function');
      expect(typeof service.updateProbeFeatures).toBe('function');
      expect(typeof service.validateProbePermissions).toBe('function');
      expect(typeof service.validateProbeLockUnlockPermissions).toBe('function');
      expect(typeof service.validateProbeEnableDisablePermissions).toBe('function');
      expect(typeof service.validateProbeRMAPermissions).toBe('function');
      expect(typeof service.downloadProbes).toBe('function');
      expect(typeof service.updateProbeType).toBe('function');
      expect(typeof service.exportProbesCSV).toBe('function');
      expect(typeof service.exportProbeHistoricalConnections).toBe('function');
      expect(typeof service.transferProbes).toBe('function');
      expect(typeof service.changeOperationForProbe).toBe('function');
    });

    it('should ensure service instance is properly configured', () => {
      expect(service).toBeInstanceOf(ProbeOperationService);
      expect(service).toBeTruthy();
    });
  });
});