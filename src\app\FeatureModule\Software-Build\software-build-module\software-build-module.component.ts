import { Component } from '@angular/core';
import { SoftwareBuildSearchRequestBody } from 'src/app/model/SoftwaarBuilds/SoftwareBuildSearchRequestBody';

@Component({
  selector: 'app-software-build-module',
  templateUrl: './software-build-module.component.html',
  styleUrl: './software-build-module.component.css'
})
export class SoftwareBuildModuleComponent {
  sofwareBuildListFilterRequestBody: SoftwareBuildSearchRequestBody = null;
  isFilterHidden: boolean = false;

  isSoftwareBuildListingPageDisplay: boolean = true;

  loading: boolean = false;


  // Properties for filter component initialization
  isFilterComponentInitWithApicall: boolean = true;


  /**
  * Update softwareBuildListFilterRequestBody from child component
  * <AUTHOR>
  * @param value
  */
  public updateSoftwareBuildListFilterRequestBody(value: SoftwareBuildSearchRequestBody): void {
    this.sofwareBuildListFilterRequestBody = value;
  }

  public isFilterComponentInitWithApicallChange(value: boolean): void {
    this.isFilterComponentInitWithApicall = value;
  }

  /**
  * Update isFilterHidden from child component
  * <AUTHOR>
  * @param value
  */
  public updateIsFilterHidden(value: boolean): void {
    this.isFilterHidden = value;
  }

}
