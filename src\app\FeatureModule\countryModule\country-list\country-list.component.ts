import { HttpErrorResponse, HttpResponse } from '@angular/common/http';
import { ChangeDetectorRef, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { isUndefined } from 'is-what';
import { ToastrService } from 'ngx-toastr';
import { Subscription } from 'rxjs';
import { COUNTRY_NOT_SELECTED, DELETE_COUNTRY_CONFIRMATION_MESSAGE, ITEMS_PER_PAGE, ListCountryResource } from 'src/app/app.constants';
import { ConfirmDialogService } from 'src/app/confirmationdialog/confirmation.service';
import { CountryFilterAction } from 'src/app/model/Country/CountryFilterAction.model';
import { CountryListResponse } from 'src/app/model/Country/CountryListResponse.model';
import { CountryPageResponse } from 'src/app/model/Country/CountryPageResponse.model';
import { CountryRequestBody } from 'src/app/model/Country/CountryRequestBody.model';
import { SuccessMessageResponse } from 'src/app/model/common/SuccessMessageResponse.model';
import { ListingPageReloadSubjectParameter } from 'src/app/model/common/listingPageReloadSubjectParameter.model';
import { ExceptionHandlingService } from 'src/app/shared/ExceptionHandling.service';
import { CountryApiCallService } from 'src/app/shared/Service/CountryService/country-api-call.service';
import { CountryService } from 'src/app/shared/Service/CountryService/country.service';
import { AuthJwtService } from 'src/app/shared/auth-jwt.service';
import { PermissionAction } from 'src/app/shared/enum/Permission/permissionAction.enum';
import { collapseFilterTextEnum } from 'src/app/shared/enum/collapseFilterButtonText.enum';
import { CreateCountryService } from 'src/app/shared/modalservice/Country/create-country.service';
import { PermissionService } from 'src/app/shared/permission.service';
import { CommonCheckboxService } from 'src/app/shared/util/common-checkbox.service';
import { CommonsService } from 'src/app/shared/util/commons.service';

@Component({
  selector: 'app-country-list',
  templateUrl: './country-list.component.html',
  styleUrls: ['./country-list.component.css'],
  encapsulation: ViewEncapsulation.None
})
export class CountryListComponent implements OnInit {
  loading: boolean = false;

  //Filter
  isFilterHidden: boolean = false;
  hideShowFilterButtonText: string = collapseFilterTextEnum.HIDE_FILTER;
  isFilterComponentInitWithApicall: boolean = true;

  //Page size dropdown
  dataSizes: string[] = [];
  drpselectsize: number = ITEMS_PER_PAGE;

  //Page
  itemsPerPage: number = 0;
  page: number = 0;
  previousPage: number = 1;
  totalItems: number = 0;

  //Subscription
  subscriptionForLoading: Subscription;
  subscriptionForRoleListFilterRequestParameter: Subscription;

  //Country List
  countryResponseList: CountryListResponse[] = [];

  //Total count display
  totalRecordDisplay: number = 0;
  totalRecord: number = 0;

  //audit serach request body store
  countryRequestBody: CountryRequestBody = null;

  //selected Country Id Collect
  selectedCountriesIdList: number[] = [];
  localCountriesIdListArray: number[] = [];


  //unique CheckBox Name
  chkPreFix = "country";
  selectAllCheckboxId = "selectAllCountry";
  checkboxListName = "CountryList[]";

  //checkboxHide
  showCheckBox: boolean = false;

  //permissions
  addCountryPermission: boolean = false;
  deleteCountryPermission: boolean = false;

  constructor(
    private authService: AuthJwtService,
    private countryApiCallService: CountryApiCallService,
    private commonsService: CommonsService,
    private countryService: CountryService,
    private exceptionService: ExceptionHandlingService,
    private commonCheckboxService: CommonCheckboxService,
    private dialogservice: ConfirmDialogService,
    private toste: ToastrService,
    private createCountryService: CreateCountryService,
    private permissionService: PermissionService,
    private cdr: ChangeDetectorRef
  ) { }

  /**
   * Init Method
   * 
   * <AUTHOR>
   */
  public ngOnInit(): void {
    if (!this.authService.isAuthenticate()) {
      this.authService.loginNavigate();
    } else {
      this.page = 0;
      this.dataSizes = this.commonsService.accessDataSizes();
      this.drpselectsize = ITEMS_PER_PAGE;
      this.itemsPerPage = ITEMS_PER_PAGE;
      this.isFilterHidden = false;
      this.showCheckBox = true;
      this.addCountryPermission = this.permissionService.getCountryPermission(PermissionAction.ADD_COUNTRY_ACTION);
      this.deleteCountryPermission = this.permissionService.getCountryPermission(PermissionAction.DELETE_COUNTRY_ACTION);
      this.isFilterComponentInitWithApicall = true;
      this.resetPage();
    }
    this.subjectInit();
  }

  /**
  * Destroy subscription
  * <AUTHOR>
  */
  public ngOnDestroy(): void {
    if (!isUndefined(this.subscriptionForRoleListFilterRequestParameter)) { this.subscriptionForRoleListFilterRequestParameter.unsubscribe() }
  }

  public refreshFilter() {
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  public subjectInit(): void {
    this.subscriptionForRoleListFilterRequestParameter = this.countryService.getCountryListFilterRequestParameterSubject()?.subscribe((countryRequestParamter: CountryFilterAction) => {
      if (countryRequestParamter.listingPageReloadSubjectParameter.isReloadData) {
        if (countryRequestParamter.listingPageReloadSubjectParameter.isDefaultPageNumber) {
          this.resetPage();
        }
        this.loadAll(countryRequestParamter.countryRequestBody);
      }
    });
  }

  /**
* Change The Page
* callRoleListRefreshSubject ->Call the filter component
* filter not clear and send with filter requrest and load data 
* <AUTHOR>
* @param page 
*/
  public loadPage(page): void {
    if (page !== this.previousPage) {
      this.previousPage = page;
      this.filterPageSubjectCallForReloadPage(false, false);
    }
  }

  public loadAll(countryRequestBody: CountryRequestBody) {
    this.countryRequestBody = countryRequestBody;
    let pageObj = {
      page: this.page - 1,
      size: this.itemsPerPage
    }
    this.setLoadingStatus(true);
    this.countryApiCallService.getCountryList(countryRequestBody, pageObj)?.subscribe({
      next: (countryPageResponse: HttpResponse<CountryPageResponse>) => {
        if (countryPageResponse.status == 200) {
          this.paginateDataset(countryPageResponse.body);
        } else {
          this.countryResponseList = [];
          this.totalRecordDisplay = 0;
          this.totalRecord = 0;
          this.loading = false;
        }
        this.setLoadingStatus(false);
      },
      error: (error: HttpErrorResponse) => {
        this.setLoadingStatus(false);
        this.exceptionService.customErrorMessage(error);
      }
    })
  }

  private paginateDataset(countryPageResponse: CountryPageResponse): void {
    this.totalItems = countryPageResponse.totalElements;
    this.countryResponseList = countryPageResponse.content;
    this.page = countryPageResponse.number + 1;
    this.totalRecord = countryPageResponse.totalElements;
    this.totalRecordDisplay = countryPageResponse.numberOfElements;
    this.setLocalSalesOrderId(this.countryResponseList);
    this.setLoadingStatus(false);
  }

  public resetPage(): void {
    this.page = 0;
    this.previousPage = 1;
  }
  public filterPageSubjectCallForReloadPage(isDefaultPageNumber: boolean, isClearFilter: boolean): void {
    let listingPageReloadSubjectParameter = new ListingPageReloadSubjectParameter(true, isDefaultPageNumber, isClearFilter, false);
    this.countryService?.callRefreshPageSubject(listingPageReloadSubjectParameter, ListCountryResource, this.isFilterHidden, this.countryRequestBody);
  }
  public toggleFilter(): void {
    this.isFilterComponentInitWithApicall = false;
    this.isFilterHidden = !this.isFilterHidden;
    if (this.isFilterHidden) {
      this.hideShowFilterButtonText = collapseFilterTextEnum.SHOW_FILTER;

    } else {
      this.hideShowFilterButtonText = collapseFilterTextEnum.HIDE_FILTER;
    }
  }

  /**
   * Select All CheckBox
   * <AUTHOR>
   * @param isChecked 
   */
  public selectAllItem(isChecked: boolean): void {
    this.selectedCountriesIdList = this.commonCheckboxService.selectAllItem(isChecked, this.localCountriesIdListArray, this.selectedCountriesIdList, this.checkboxListName);
  }

  /**
   * single Checkbox Select
   * <AUTHOR>
   * @param salesOrderObj 
   * @param isChecked 
   */
  public selectCheckbox(countryObj: CountryListResponse, isChecked: boolean): void {
    if (isChecked) {
      this.selectedCountriesIdList.push(countryObj.id);
    } else {
      let index = this.selectedCountriesIdList.findIndex(obj => obj == countryObj.id);
      this.selectedCountriesIdList.splice(index, 1);
    }
    this.defaultSelectAll();
  }

  /**
   * Local Sales Order Id list create for Select all Checkbox
   * <AUTHOR>
   * @param CountryIdList 
   */
  public setLocalSalesOrderId(CountryIdList: CountryListResponse[]): void {
    this.localCountriesIdListArray = [];
    for (let countryOrderObj of CountryIdList) {
      this.localCountriesIdListArray.push(countryOrderObj.id);
    }
    this.defaultSelectAll();
  }

  /**
  * select All checkbox select or deSelect
  * <AUTHOR>
  */
  private defaultSelectAll(): void {
    this.commonCheckboxService.defaultSelectAll(this.localCountriesIdListArray, this.selectedCountriesIdList, this.selectAllCheckboxId);
  }

  public changeDataSize(dataSize): void {
    this.setLoadingStatus(true);
    this.itemsPerPage = dataSize.target.value;
    this.filterPageSubjectCallForReloadPage(true, false);
  }

  /**
  * Add Country
  * <AUTHOR>
  */
  public addCountry(): void {
    this.createCountryService.openAddCountryPopup(this.createCountryService.getCreateCountryParameter(ListCountryResource, this.isFilterHidden)).finally(() => { });
  }

  /**
  * Loading Status 
  * <AUTHOR>
  */
  public setLoadingStatus(status: boolean): void {
    this.loading = status;
    this.cdr.detectChanges();
  }

  /**
  * Deletes the selected countries after user confirmation.
  *
  * Prompts the user with a confirmation dialog before initiating the delete operation.
  * If confirmed, it calls the API to delete the selected countries, handles the response,
  * and updates the view accordingly. Displays an informational message if no countries are selected.
  *
  * <AUTHOR>
  */
  public deleteCountry(): void {
    if (this.selectedCountriesIdList.length > 0) {
      this.dialogservice.confirm('Delete', DELETE_COUNTRY_CONFIRMATION_MESSAGE)
        .then((confirmed) => {
          if (confirmed) {
            this.setLoadingStatus(true);
            this.countryApiCallService.deleteCountry(this.selectedCountriesIdList)?.subscribe({
              next: (res: HttpResponse<SuccessMessageResponse>) => {
                this.toste.success(res.body.message);
                this.filterPageSubjectCallForReloadPage(false, false);
                this.selectedCountriesIdList = [];
              },
              error: (error: HttpErrorResponse) => {
                this.setLoadingStatus(false);
                this.exceptionService.customErrorMessage(error);
              }
            })
          }
        });
    } else {
      this.toste.info(COUNTRY_NOT_SELECTED);
    }
  }
}
